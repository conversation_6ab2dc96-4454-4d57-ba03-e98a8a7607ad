-- =====================================================================================
-- SCHÉMA UNIFIÉ POUR APPLICATION DE NÉO-BANQUE
-- Combinaison optimisée des 5 propositions avec améliorations sécuritaires
-- =====================================================================================

-- Configuration de base
SET FOREIGN_KEY_CHECKS=0;
SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

CREATE DATABASE IF NOT EXISTS neobank_unified 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE neobank_unified;

-- =====================================================================================
-- 1. SYSTÈME DE RÔLES ET PERMISSIONS (RBAC)
-- =====================================================================================

CREATE TABLE `roles` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(50) UNIQUE NOT NULL COMMENT 'client, manager, admin, super_admin',
  `display_name` VARCHAR(100) NOT NULL,
  `description` TEXT NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_roles_name` (`name`),
  INDEX `idx_roles_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `permissions` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) UNIQUE NOT NULL,
  `resource` VARCHAR(50) NOT NULL COMMENT 'user, account, transaction, etc.',
  `action` VARCHAR(50) NOT NULL COMMENT 'create, read, update, delete, etc.',
  `description` TEXT NULL,
  `is_system` BOOLEAN NOT NULL DEFAULT FALSE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_permissions_resource_action` (`resource`, `action`),
  INDEX `idx_permissions_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `role_permissions` (
  `role_id` INT UNSIGNED NOT NULL,
  `permission_id` INT UNSIGNED NOT NULL,
  `granted_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `granted_by` BIGINT UNSIGNED NULL,
  PRIMARY KEY (`role_id`, `permission_id`),
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 2. GESTION DES UTILISATEURS
-- =====================================================================================

CREATE TABLE `users` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
  `role_id` INT UNSIGNED NOT NULL,
  
  -- Informations personnelles
  `email` VARCHAR(255) UNIQUE NOT NULL,
  `password_hash` VARCHAR(255) NOT NULL,
  `first_name` VARCHAR(100) NOT NULL,
  `last_name` VARCHAR(100) NOT NULL,
  `phone` VARCHAR(30) NULL,
  `date_of_birth` DATE NULL,
  
  -- Adresse
  `address_street` VARCHAR(255) NULL,
  `address_city` VARCHAR(100) NULL,
  `address_postal_code` VARCHAR(20) NULL,
  `address_country` VARCHAR(100) DEFAULT 'France',
  `address_country_code` CHAR(2) DEFAULT 'FR',
  
  -- Préférences
  `preferred_language` CHAR(2) NOT NULL DEFAULT 'fr',
  `timezone` VARCHAR(50) DEFAULT 'Europe/Paris',
  
  -- Sécurité et authentification
  `email_verified_at` TIMESTAMP NULL,
  `email_verification_token` VARCHAR(255) NULL UNIQUE,
  `password_reset_token` VARCHAR(255) NULL UNIQUE,
  `password_reset_expires_at` TIMESTAMP NULL,
  `two_factor_enabled` BOOLEAN NOT NULL DEFAULT FALSE,
  `two_factor_secret` VARCHAR(255) NULL,
  `backup_codes` JSON NULL,
  
  -- Gestion des tentatives de connexion
  `failed_login_attempts` TINYINT UNSIGNED DEFAULT 0,
  `locked_until` TIMESTAMP NULL,
  `last_login_at` TIMESTAMP NULL,
  `last_login_ip` VARCHAR(45) NULL,
  `last_activity_at` TIMESTAMP NULL,
  
  -- Relations gestionnaire/client
  `assigned_manager_id` BIGINT UNSIGNED NULL,
  `referral_code` VARCHAR(50) NULL UNIQUE,
  `referred_by_manager_id` BIGINT UNSIGNED NULL,
  `referral_bonus_paid` BOOLEAN DEFAULT FALSE,
  
  -- Statut et métadonnées
  `status` ENUM('pending_verification', 'active', 'suspended', 'closed', 'dormant') NOT NULL DEFAULT 'pending_verification',
  `kyc_status` ENUM('not_started', 'pending', 'approved', 'rejected', 'expired') NOT NULL DEFAULT 'not_started',
  `kyc_level` TINYINT UNSIGNED DEFAULT 0 COMMENT '0=none, 1=basic, 2=enhanced, 3=full',
  `risk_score` DECIMAL(3,2) DEFAULT 0.00 COMMENT 'Score de risque 0.00-1.00',
  
  -- Consentements et conformité
  `terms_accepted_at` TIMESTAMP NULL,
  `privacy_policy_accepted_at` TIMESTAMP NULL,
  `marketing_consent` BOOLEAN DEFAULT FALSE,
  `data_retention_consent` BOOLEAN DEFAULT TRUE,
  
  -- Horodatage
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL,
  
  -- Index et contraintes
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`assigned_manager_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`referred_by_manager_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  
  INDEX `idx_users_email` (`email`),
  INDEX `idx_users_uuid` (`uuid`),
  INDEX `idx_users_status` (`status`),
  INDEX `idx_users_kyc_status` (`kyc_status`),
  INDEX `idx_users_manager` (`assigned_manager_id`),
  INDEX `idx_users_referral` (`referral_code`),
  INDEX `idx_users_last_activity` (`last_activity_at`),
  INDEX `idx_users_deleted` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 3. TYPES DE COMPTES ET COMPTES BANCAIRES
-- =====================================================================================

CREATE TABLE `account_types` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) UNIQUE NOT NULL,
  `code` VARCHAR(20) UNIQUE NOT NULL,
  `description` TEXT NULL,
  `category` ENUM('checking', 'savings', 'business', 'premium') NOT NULL,
  
  -- Paramètres financiers
  `interest_rate` DECIMAL(5,4) DEFAULT 0.0000,
  `minimum_balance` DECIMAL(15,2) DEFAULT 0.00,
  `maximum_balance` DECIMAL(15,2) NULL,
  `monthly_fee` DECIMAL(10,2) DEFAULT 0.00,
  `overdraft_limit` DECIMAL(15,2) DEFAULT 0.00,
  `overdraft_fee` DECIMAL(10,2) DEFAULT 0.00,
  
  -- Limites de transaction
  `daily_transfer_limit` DECIMAL(15,2) DEFAULT 5000.00,
  `monthly_transfer_limit` DECIMAL(15,2) DEFAULT 50000.00,
  `daily_withdrawal_limit` DECIMAL(15,2) DEFAULT 1000.00,
  
  -- Configuration
  `features` JSON NULL COMMENT 'Fonctionnalités spécifiques',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `requires_kyc_level` TINYINT UNSIGNED DEFAULT 1,
  
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX `idx_account_types_code` (`code`),
  INDEX `idx_account_types_category` (`category`),
  INDEX `idx_account_types_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `accounts` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NOT NULL,
  `account_type_id` INT UNSIGNED NOT NULL,
  
  -- Identifiants bancaires
  `account_number` VARCHAR(50) UNIQUE NOT NULL,
  `iban` VARCHAR(34) UNIQUE NOT NULL,
  `bic` VARCHAR(11) DEFAULT 'NEOBANKFR1',
  
  -- Informations du compte
  `friendly_name` VARCHAR(100) NULL COMMENT 'Nom personnalisé par l''utilisateur',
  `balance` DECIMAL(19,4) NOT NULL DEFAULT 0.0000,
  `available_balance` DECIMAL(19,4) NOT NULL DEFAULT 0.0000,
  `reserved_balance` DECIMAL(19,4) NOT NULL DEFAULT 0.0000,
  `currency` CHAR(3) NOT NULL DEFAULT 'EUR',
  
  -- Statut et configuration
  `status` ENUM('pending_approval', 'active', 'dormant', 'suspended', 'closed') NOT NULL DEFAULT 'pending_approval',
  `is_primary` BOOLEAN NOT NULL DEFAULT FALSE,
  `overdraft_authorized` BOOLEAN NOT NULL DEFAULT FALSE,
  `overdraft_limit` DECIMAL(15,2) DEFAULT 0.00,
  
  -- Limites personnalisées (peuvent surcharger celles du type)
  `custom_daily_limit` DECIMAL(15,2) NULL,
  `custom_monthly_limit` DECIMAL(15,2) NULL,
  
  -- Métadonnées
  `opened_by` BIGINT UNSIGNED NULL,
  `closed_by` BIGINT UNSIGNED NULL,
  `closure_reason` VARCHAR(255) NULL,
  
  -- Horodatage
  `opened_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `closed_at` TIMESTAMP NULL,
  `last_transaction_at` TIMESTAMP NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Contraintes et index
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`account_type_id`) REFERENCES `account_types`(`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`opened_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`closed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  
  INDEX `idx_accounts_user` (`user_id`),
  INDEX `idx_accounts_uuid` (`uuid`),
  INDEX `idx_accounts_account_number` (`account_number`),
  INDEX `idx_accounts_iban` (`iban`),
  INDEX `idx_accounts_status` (`status`),
  INDEX `idx_accounts_type` (`account_type_id`),
  INDEX `idx_accounts_primary` (`user_id`, `is_primary`),
  INDEX `idx_accounts_last_transaction` (`last_transaction_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 4. CATÉGORIES ET TYPES DE TRANSACTIONS
-- =====================================================================================

CREATE TABLE `transaction_categories` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `code` VARCHAR(30) UNIQUE NOT NULL,
  `description` TEXT NULL,
  `color` VARCHAR(7) DEFAULT '#2196F3' COMMENT 'Code couleur hexadécimal',
  `icon` VARCHAR(50) NULL,
  `is_system` BOOLEAN NOT NULL DEFAULT FALSE,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `parent_category_id` INT UNSIGNED NULL,
  `sort_order` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (`parent_category_id`) REFERENCES `transaction_categories`(`id`) ON DELETE SET NULL,
  INDEX `idx_transaction_categories_code` (`code`),
  INDEX `idx_transaction_categories_parent` (`parent_category_id`),
  INDEX `idx_transaction_categories_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `transaction_types` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `code` VARCHAR(30) UNIQUE NOT NULL,
  `description` TEXT NULL,
  `direction` ENUM('credit', 'debit', 'both') NOT NULL,
  `requires_approval` BOOLEAN NOT NULL DEFAULT FALSE,
  `max_amount` DECIMAL(15,2) NULL,
  `fee_percentage` DECIMAL(5,4) DEFAULT 0.0000,
  `fixed_fee` DECIMAL(10,2) DEFAULT 0.00,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX `idx_transaction_types_code` (`code`),
  INDEX `idx_transaction_types_direction` (`direction`),
  INDEX `idx_transaction_types_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 5. TRANSACTIONS
-- =====================================================================================

CREATE TABLE `transactions` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
  `transaction_type_id` INT UNSIGNED NOT NULL,
  `category_id` INT UNSIGNED NULL,

  -- Comptes impliqués
  `source_account_id` BIGINT UNSIGNED NULL,
  `destination_account_id` BIGINT UNSIGNED NULL,

  -- Montants et devise
  `amount` DECIMAL(19,4) NOT NULL,
  `currency` CHAR(3) NOT NULL DEFAULT 'EUR',
  `fees` DECIMAL(19,4) DEFAULT 0.0000,
  `exchange_rate` DECIMAL(12,6) DEFAULT 1.000000,
  `original_amount` DECIMAL(19,4) NULL COMMENT 'Montant original si change',
  `original_currency` CHAR(3) NULL,

  -- Informations de la transaction
  `reference_number` VARCHAR(100) UNIQUE NOT NULL,
  `description` TEXT NULL,
  `merchant_name` VARCHAR(200) NULL,
  `merchant_category` VARCHAR(50) NULL,

  -- Bénéficiaire externe (si applicable)
  `external_beneficiary_name` VARCHAR(200) NULL,
  `external_beneficiary_iban` VARCHAR(34) NULL,
  `external_beneficiary_bic` VARCHAR(11) NULL,
  `external_beneficiary_bank` VARCHAR(200) NULL,

  -- Statut et traitement
  `status` ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed', 'on_hold') NOT NULL DEFAULT 'pending',
  `failure_reason` VARCHAR(255) NULL,
  `requires_approval` BOOLEAN NOT NULL DEFAULT FALSE,
  `approved_by` BIGINT UNSIGNED NULL,
  `approved_at` TIMESTAMP NULL,

  -- Dates importantes
  `initiated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `processed_at` TIMESTAMP NULL,
  `value_date` DATE NULL,
  `booking_date` DATE NULL,

  -- Traçabilité
  `initiated_by` BIGINT UNSIGNED NULL,
  `processed_by` BIGINT UNSIGNED NULL,
  `channel` ENUM('web', 'mobile', 'api', 'batch', 'admin') DEFAULT 'web',
  `device_fingerprint` VARCHAR(255) NULL,
  `ip_address` VARCHAR(45) NULL,

  -- Métadonnées
  `metadata` JSON NULL COMMENT 'Données supplémentaires spécifiques',
  `tags` JSON NULL COMMENT 'Tags pour catégorisation',

  -- Réconciliation et comptabilité
  `reconciled` BOOLEAN NOT NULL DEFAULT FALSE,
  `reconciled_at` TIMESTAMP NULL,
  `accounting_entry_id` BIGINT UNSIGNED NULL,

  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Contraintes et index
  FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_types`(`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`category_id`) REFERENCES `transaction_categories`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`source_account_id`) REFERENCES `accounts`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`destination_account_id`) REFERENCES `accounts`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`initiated_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`processed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`approved_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  INDEX `idx_transactions_uuid` (`uuid`),
  INDEX `idx_transactions_source_account` (`source_account_id`),
  INDEX `idx_transactions_destination_account` (`destination_account_id`),
  INDEX `idx_transactions_status` (`status`),
  INDEX `idx_transactions_type` (`transaction_type_id`),
  INDEX `idx_transactions_reference` (`reference_number`),
  INDEX `idx_transactions_amount` (`amount`),
  INDEX `idx_transactions_initiated_at` (`initiated_at`),
  INDEX `idx_transactions_processed_at` (`processed_at`),
  INDEX `idx_transactions_value_date` (`value_date`),
  INDEX `idx_transactions_reconciled` (`reconciled`),
  INDEX `idx_transactions_approval` (`requires_approval`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 6. BÉNÉFICIAIRES
-- =====================================================================================

CREATE TABLE `beneficiaries` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NOT NULL,

  -- Informations du bénéficiaire
  `name` VARCHAR(200) NOT NULL,
  `nickname` VARCHAR(100) NULL COMMENT 'Surnom donné par l''utilisateur',
  `account_number` VARCHAR(34) NOT NULL,
  `iban` VARCHAR(34) NULL,
  `bic` VARCHAR(11) NULL,
  `bank_name` VARCHAR(200) NULL,
  `bank_address` TEXT NULL,
  `bank_country` VARCHAR(100) NULL,

  -- Classification
  `is_internal` BOOLEAN NOT NULL DEFAULT FALSE,
  `internal_account_id` BIGINT UNSIGNED NULL,
  `is_favorite` BOOLEAN NOT NULL DEFAULT FALSE,
  `category` ENUM('personal', 'business', 'utility', 'government', 'other') DEFAULT 'personal',

  -- Vérification et sécurité
  `is_verified` BOOLEAN NOT NULL DEFAULT FALSE,
  `verification_method` ENUM('manual', 'automatic', 'admin') NULL,
  `verified_at` TIMESTAMP NULL,
  `verified_by` BIGINT UNSIGNED NULL,
  `risk_score` DECIMAL(3,2) DEFAULT 0.00,

  -- Limites et restrictions
  `daily_limit` DECIMAL(15,2) NULL,
  `monthly_limit` DECIMAL(15,2) NULL,
  `is_blocked` BOOLEAN NOT NULL DEFAULT FALSE,
  `blocked_reason` VARCHAR(255) NULL,
  `blocked_at` TIMESTAMP NULL,

  -- Métadonnées
  `notes` TEXT NULL,
  `tags` JSON NULL,
  `last_used_at` TIMESTAMP NULL,
  `usage_count` INT UNSIGNED DEFAULT 0,

  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL,

  -- Contraintes et index
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`internal_account_id`) REFERENCES `accounts`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`verified_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  UNIQUE KEY `unique_user_account` (`user_id`, `account_number`),
  INDEX `idx_beneficiaries_uuid` (`uuid`),
  INDEX `idx_beneficiaries_user` (`user_id`),
  INDEX `idx_beneficiaries_iban` (`iban`),
  INDEX `idx_beneficiaries_internal` (`is_internal`),
  INDEX `idx_beneficiaries_favorite` (`user_id`, `is_favorite`),
  INDEX `idx_beneficiaries_verified` (`is_verified`),
  INDEX `idx_beneficiaries_deleted` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 7. VIREMENTS PROGRAMMÉS
-- =====================================================================================

CREATE TABLE `scheduled_transfers` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NOT NULL,
  `source_account_id` BIGINT UNSIGNED NOT NULL,

  -- Destination
  `destination_account_id` BIGINT UNSIGNED NULL COMMENT 'Pour virements internes',
  `beneficiary_id` BIGINT UNSIGNED NULL COMMENT 'Pour virements externes',

  -- Montant et devise
  `amount` DECIMAL(19,4) NOT NULL,
  `currency` CHAR(3) NOT NULL DEFAULT 'EUR',
  `description` TEXT NULL,
  `reference` VARCHAR(100) NULL,

  -- Programmation
  `frequency` ENUM('once', 'daily', 'weekly', 'bi_weekly', 'monthly', 'quarterly', 'semi_annual', 'annual') NOT NULL,
  `start_date` DATE NOT NULL,
  `end_date` DATE NULL,
  `next_execution_date` DATE NOT NULL,
  `last_execution_date` DATE NULL,
  `execution_count` INT UNSIGNED DEFAULT 0,
  `max_executions` INT UNSIGNED NULL,

  -- Configuration avancée
  `execution_day_of_month` TINYINT UNSIGNED NULL COMMENT 'Jour du mois (1-31)',
  `execution_day_of_week` TINYINT UNSIGNED NULL COMMENT 'Jour de la semaine (1-7)',
  `skip_weekends` BOOLEAN NOT NULL DEFAULT TRUE,
  `skip_holidays` BOOLEAN NOT NULL DEFAULT TRUE,

  -- Statut et gestion
  `status` ENUM('active', 'paused', 'completed', 'cancelled', 'failed') NOT NULL DEFAULT 'active',
  `failure_count` TINYINT UNSIGNED DEFAULT 0,
  `last_failure_reason` VARCHAR(255) NULL,
  `auto_retry` BOOLEAN NOT NULL DEFAULT TRUE,
  `max_retries` TINYINT UNSIGNED DEFAULT 3,

  -- Notifications
  `notify_on_execution` BOOLEAN NOT NULL DEFAULT TRUE,
  `notify_on_failure` BOOLEAN NOT NULL DEFAULT TRUE,
  `notification_email` VARCHAR(255) NULL,

  -- Métadonnées
  `created_by` BIGINT UNSIGNED NULL,
  `modified_by` BIGINT UNSIGNED NULL,
  `notes` TEXT NULL,

  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL,

  -- Contraintes et index
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`source_account_id`) REFERENCES `accounts`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`destination_account_id`) REFERENCES `accounts`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`beneficiary_id`) REFERENCES `beneficiaries`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`modified_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  -- Contrainte : soit destination_account_id soit beneficiary_id doit être défini
  CONSTRAINT `chk_scheduled_transfers_destination` CHECK (
    (`destination_account_id` IS NOT NULL AND `beneficiary_id` IS NULL) OR
    (`destination_account_id` IS NULL AND `beneficiary_id` IS NOT NULL)
  ),

  INDEX `idx_scheduled_transfers_uuid` (`uuid`),
  INDEX `idx_scheduled_transfers_user` (`user_id`),
  INDEX `idx_scheduled_transfers_source_account` (`source_account_id`),
  INDEX `idx_scheduled_transfers_status` (`status`),
  INDEX `idx_scheduled_transfers_next_execution` (`next_execution_date`, `status`),
  INDEX `idx_scheduled_transfers_frequency` (`frequency`),
  INDEX `idx_scheduled_transfers_deleted` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 8. SYSTÈME DE NOTIFICATIONS
-- =====================================================================================

CREATE TABLE `notification_templates` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) UNIQUE NOT NULL,
  `type` ENUM('email', 'sms', 'push', 'in_app') NOT NULL,
  `subject_template` VARCHAR(255) NULL,
  `body_template` TEXT NOT NULL,
  `variables` JSON NULL COMMENT 'Variables disponibles pour le template',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX `idx_notification_templates_name` (`name`),
  INDEX `idx_notification_templates_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `notifications` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NOT NULL,
  `template_id` INT UNSIGNED NULL,

  -- Contenu
  `type` ENUM('info', 'success', 'warning', 'error', 'security') NOT NULL DEFAULT 'info',
  `category` VARCHAR(50) NOT NULL COMMENT 'transaction, security, account, system, etc.',
  `title` VARCHAR(255) NOT NULL,
  `message` TEXT NOT NULL,
  `action_url` VARCHAR(500) NULL,
  `action_text` VARCHAR(100) NULL,

  -- Canaux de diffusion
  `channels` JSON NOT NULL COMMENT 'web, email, sms, push',
  `priority` ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',

  -- État et traitement
  `is_read` BOOLEAN NOT NULL DEFAULT FALSE,
  `read_at` TIMESTAMP NULL,
  `is_sent` BOOLEAN NOT NULL DEFAULT FALSE,
  `sent_at` TIMESTAMP NULL,
  `delivery_status` JSON NULL COMMENT 'Statut de livraison par canal',

  -- Expiration et archivage
  `expires_at` TIMESTAMP NULL,
  `archived_at` TIMESTAMP NULL,

  -- Métadonnées
  `data` JSON NULL COMMENT 'Données contextuelles',
  `related_entity_type` VARCHAR(50) NULL,
  `related_entity_id` BIGINT UNSIGNED NULL,

  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Contraintes et index
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`template_id`) REFERENCES `notification_templates`(`id`) ON DELETE SET NULL,

  INDEX `idx_notifications_uuid` (`uuid`),
  INDEX `idx_notifications_user` (`user_id`),
  INDEX `idx_notifications_user_unread` (`user_id`, `is_read`),
  INDEX `idx_notifications_type` (`type`),
  INDEX `idx_notifications_category` (`category`),
  INDEX `idx_notifications_priority` (`priority`),
  INDEX `idx_notifications_expires` (`expires_at`),
  INDEX `idx_notifications_related` (`related_entity_type`, `related_entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 9. MESSAGERIE SÉCURISÉE
-- =====================================================================================

CREATE TABLE `message_threads` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
  `subject` VARCHAR(255) NOT NULL,
  `participants` JSON NOT NULL COMMENT 'Liste des IDs des participants',
  `thread_type` ENUM('support', 'client_manager', 'internal', 'system') NOT NULL,
  `status` ENUM('open', 'closed', 'archived') NOT NULL DEFAULT 'open',
  `priority` ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
  `tags` JSON NULL,
  `created_by` BIGINT UNSIGNED NOT NULL,
  `assigned_to` BIGINT UNSIGNED NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `closed_at` TIMESTAMP NULL,

  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`assigned_to`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  INDEX `idx_message_threads_uuid` (`uuid`),
  INDEX `idx_message_threads_type` (`thread_type`),
  INDEX `idx_message_threads_status` (`status`),
  INDEX `idx_message_threads_assigned` (`assigned_to`),
  INDEX `idx_message_threads_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `messages` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
  `thread_id` BIGINT UNSIGNED NOT NULL,
  `sender_id` BIGINT UNSIGNED NOT NULL,
  `message` TEXT NOT NULL,
  `message_type` ENUM('text', 'file', 'system') NOT NULL DEFAULT 'text',

  -- Pièces jointes
  `attachments` JSON NULL COMMENT 'Liste des fichiers joints',
  `attachment_count` TINYINT UNSIGNED DEFAULT 0,

  -- État de lecture
  `read_by` JSON NULL COMMENT 'Qui a lu le message et quand',
  `is_system_message` BOOLEAN NOT NULL DEFAULT FALSE,

  -- Métadonnées
  `ip_address` VARCHAR(45) NULL,
  `user_agent` TEXT NULL,
  `edited_at` TIMESTAMP NULL,
  `edited_by` BIGINT UNSIGNED NULL,

  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL,

  -- Contraintes et index
  FOREIGN KEY (`thread_id`) REFERENCES `message_threads`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`sender_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`edited_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  INDEX `idx_messages_uuid` (`uuid`),
  INDEX `idx_messages_thread` (`thread_id`),
  INDEX `idx_messages_sender` (`sender_id`),
  INDEX `idx_messages_created_at` (`created_at`),
  INDEX `idx_messages_deleted` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 10. SÉCURITÉ ET AUTHENTIFICATION
-- =====================================================================================

CREATE TABLE `refresh_tokens` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT UNSIGNED NOT NULL,
  `token_hash` VARCHAR(255) UNIQUE NOT NULL,
  `device_id` VARCHAR(255) NULL,
  `device_name` VARCHAR(100) NULL,
  `user_agent` TEXT NULL,
  `ip_address` VARCHAR(45) NULL,
  `expires_at` TIMESTAMP NOT NULL,
  `last_used_at` TIMESTAMP NULL,
  `is_revoked` BOOLEAN NOT NULL DEFAULT FALSE,
  `revoked_at` TIMESTAMP NULL,
  `revoked_by` BIGINT UNSIGNED NULL,
  `revocation_reason` VARCHAR(255) NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`revoked_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  INDEX `idx_refresh_tokens_user` (`user_id`),
  INDEX `idx_refresh_tokens_token` (`token_hash`),
  INDEX `idx_refresh_tokens_expires` (`expires_at`),
  INDEX `idx_refresh_tokens_device` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_sessions` (
  `id` VARCHAR(255) PRIMARY KEY,
  `user_id` BIGINT UNSIGNED NOT NULL,
  `ip_address` VARCHAR(45) NOT NULL,
  `user_agent` TEXT NULL,
  `device_fingerprint` VARCHAR(255) NULL,
  `location_country` VARCHAR(100) NULL,
  `location_city` VARCHAR(100) NULL,
  `is_suspicious` BOOLEAN NOT NULL DEFAULT FALSE,
  `risk_score` DECIMAL(3,2) DEFAULT 0.00,
  `last_activity_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `expires_at` TIMESTAMP NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,

  INDEX `idx_user_sessions_user` (`user_id`),
  INDEX `idx_user_sessions_expires` (`expires_at`),
  INDEX `idx_user_sessions_last_activity` (`last_activity_at`),
  INDEX `idx_user_sessions_suspicious` (`is_suspicious`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `security_events` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT UNSIGNED NULL,
  `event_type` VARCHAR(100) NOT NULL COMMENT 'login_failed, suspicious_activity, etc.',
  `severity` ENUM('low', 'medium', 'high', 'critical') NOT NULL,
  `description` TEXT NOT NULL,
  `ip_address` VARCHAR(45) NULL,
  `user_agent` TEXT NULL,
  `device_fingerprint` VARCHAR(255) NULL,
  `location_data` JSON NULL,
  `additional_data` JSON NULL,
  `is_resolved` BOOLEAN NOT NULL DEFAULT FALSE,
  `resolved_at` TIMESTAMP NULL,
  `resolved_by` BIGINT UNSIGNED NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`resolved_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  INDEX `idx_security_events_user` (`user_id`),
  INDEX `idx_security_events_type` (`event_type`),
  INDEX `idx_security_events_severity` (`severity`),
  INDEX `idx_security_events_resolved` (`is_resolved`),
  INDEX `idx_security_events_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 11. AUDIT ET JOURNALISATION
-- =====================================================================================

CREATE TABLE `audit_logs` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NULL,
  `session_id` VARCHAR(255) NULL,

  -- Action et contexte
  `action` VARCHAR(100) NOT NULL,
  `resource_type` VARCHAR(50) NOT NULL,
  `resource_id` VARCHAR(50) NULL,
  `resource_name` VARCHAR(200) NULL,

  -- Détails de l'action
  `description` TEXT NULL,
  `old_values` JSON NULL,
  `new_values` JSON NULL,
  `changes` JSON NULL COMMENT 'Résumé des changements',

  -- Contexte technique
  `ip_address` VARCHAR(45) NULL,
  `user_agent` TEXT NULL,
  `request_id` VARCHAR(100) NULL,
  `api_endpoint` VARCHAR(255) NULL,
  `http_method` VARCHAR(10) NULL,

  -- Classification
  `severity` ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
  `category` VARCHAR(50) NOT NULL COMMENT 'authentication, transaction, admin, etc.',
  `tags` JSON NULL,

  -- Résultat
  `success` BOOLEAN NOT NULL DEFAULT TRUE,
  `error_message` TEXT NULL,
  `duration_ms` INT UNSIGNED NULL,

  -- Métadonnées
  `metadata` JSON NULL,
  `compliance_flags` JSON NULL COMMENT 'Flags pour conformité réglementaire',

  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  -- Contraintes et index
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  INDEX `idx_audit_logs_uuid` (`uuid`),
  INDEX `idx_audit_logs_user` (`user_id`),
  INDEX `idx_audit_logs_action` (`action`),
  INDEX `idx_audit_logs_resource` (`resource_type`, `resource_id`),
  INDEX `idx_audit_logs_severity` (`severity`),
  INDEX `idx_audit_logs_category` (`category`),
  INDEX `idx_audit_logs_created_at` (`created_at`),
  INDEX `idx_audit_logs_success` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 12. CONFIGURATION ET PARAMÈTRES SYSTÈME
-- =====================================================================================

CREATE TABLE `system_settings` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `setting_key` VARCHAR(100) UNIQUE NOT NULL,
  `setting_value` TEXT NULL,
  `data_type` ENUM('string', 'integer', 'float', 'boolean', 'json', 'encrypted') NOT NULL DEFAULT 'string',
  `category` VARCHAR(50) NOT NULL DEFAULT 'general',
  `description` TEXT NULL,
  `is_public` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Visible côté client',
  `is_editable` BOOLEAN NOT NULL DEFAULT TRUE,
  `validation_rules` JSON NULL COMMENT 'Règles de validation',
  `default_value` TEXT NULL,
  `updated_by` BIGINT UNSIGNED NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (`updated_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  INDEX `idx_system_settings_key` (`setting_key`),
  INDEX `idx_system_settings_category` (`category`),
  INDEX `idx_system_settings_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 13. FAQ ET SUPPORT
-- =====================================================================================

CREATE TABLE `faq_categories` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) NOT NULL,
  `slug` VARCHAR(100) UNIQUE NOT NULL,
  `description` TEXT NULL,
  `icon` VARCHAR(50) NULL,
  `sort_order` INT DEFAULT 0,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX `idx_faq_categories_slug` (`slug`),
  INDEX `idx_faq_categories_active` (`is_active`),
  INDEX `idx_faq_categories_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `faq_entries` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `category_id` INT UNSIGNED NULL,
  `question` TEXT NOT NULL,
  `answer` TEXT NOT NULL,
  `language` CHAR(2) NOT NULL DEFAULT 'fr',
  `tags` JSON NULL,
  `sort_order` INT DEFAULT 0,
  `view_count` INT UNSIGNED DEFAULT 0,
  `is_featured` BOOLEAN NOT NULL DEFAULT FALSE,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_by` BIGINT UNSIGNED NULL,
  `updated_by` BIGINT UNSIGNED NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (`category_id`) REFERENCES `faq_categories`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`updated_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  INDEX `idx_faq_entries_category` (`category_id`),
  INDEX `idx_faq_entries_language` (`language`),
  INDEX `idx_faq_entries_active` (`is_active`),
  INDEX `idx_faq_entries_featured` (`is_featured`),
  INDEX `idx_faq_entries_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `support_tickets` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL DEFAULT (UUID()),
  `ticket_number` VARCHAR(20) UNIQUE NOT NULL,
  `user_id` BIGINT UNSIGNED NULL,
  `guest_email` VARCHAR(255) NULL,
  `guest_name` VARCHAR(200) NULL,

  -- Contenu
  `subject` VARCHAR(255) NOT NULL,
  `description` TEXT NOT NULL,
  `category` VARCHAR(50) NOT NULL,
  `priority` ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',

  -- Assignation et statut
  `status` ENUM('open', 'pending_customer', 'pending_agent', 'in_progress', 'resolved', 'closed') NOT NULL DEFAULT 'open',
  `assigned_to` BIGINT UNSIGNED NULL,
  `assigned_team` VARCHAR(50) NULL,

  -- Résolution
  `resolution` TEXT NULL,
  `satisfaction_rating` TINYINT UNSIGNED NULL COMMENT '1-5 stars',
  `satisfaction_comment` TEXT NULL,

  -- Métadonnées
  `source` ENUM('web', 'email', 'phone', 'chat', 'mobile') NOT NULL DEFAULT 'web',
  `tags` JSON NULL,
  `attachments` JSON NULL,

  -- Dates importantes
  `first_response_at` TIMESTAMP NULL,
  `resolved_at` TIMESTAMP NULL,
  `closed_at` TIMESTAMP NULL,
  `due_date` TIMESTAMP NULL,

  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Contraintes et index
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`assigned_to`) REFERENCES `users`(`id`) ON DELETE SET NULL,

  INDEX `idx_support_tickets_uuid` (`uuid`),
  INDEX `idx_support_tickets_number` (`ticket_number`),
  INDEX `idx_support_tickets_user` (`user_id`),
  INDEX `idx_support_tickets_status` (`status`),
  INDEX `idx_support_tickets_assigned` (`assigned_to`),
  INDEX `idx_support_tickets_priority` (`priority`),
  INDEX `idx_support_tickets_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================================================
-- 14. DONNÉES INITIALES
-- =====================================================================================

-- Réactiver les vérifications de clés étrangères
SET FOREIGN_KEY_CHECKS=1;

-- Insertion des rôles de base
INSERT INTO `roles` (`name`, `display_name`, `description`) VALUES
('client', 'Client', 'Utilisateur client standard de la néo-banque'),
('manager', 'Gestionnaire', 'Gestionnaire de comptes clients'),
('admin', 'Administrateur', 'Administrateur de la plateforme'),
('super_admin', 'Super Administrateur', 'Super administrateur avec tous les droits');

-- Insertion des permissions de base
INSERT INTO `permissions` (`name`, `resource`, `action`, `description`, `is_system`) VALUES
-- Permissions utilisateur
('user.read.own', 'user', 'read', 'Consulter ses propres informations', TRUE),
('user.update.own', 'user', 'update', 'Modifier ses propres informations', TRUE),
('user.read.all', 'user', 'read', 'Consulter tous les utilisateurs', TRUE),
('user.update.all', 'user', 'update', 'Modifier tous les utilisateurs', TRUE),
('user.delete', 'user', 'delete', 'Supprimer un utilisateur', TRUE),
('user.create', 'user', 'create', 'Créer un utilisateur', TRUE),

-- Permissions compte
('account.read.own', 'account', 'read', 'Consulter ses propres comptes', TRUE),
('account.read.assigned', 'account', 'read', 'Consulter les comptes des clients assignés', TRUE),
('account.read.all', 'account', 'read', 'Consulter tous les comptes', TRUE),
('account.create', 'account', 'create', 'Créer un compte', TRUE),
('account.update', 'account', 'update', 'Modifier un compte', TRUE),
('account.suspend', 'account', 'suspend', 'Suspendre un compte', TRUE),
('account.close', 'account', 'close', 'Fermer un compte', TRUE),

-- Permissions transaction
('transaction.read.own', 'transaction', 'read', 'Consulter ses propres transactions', TRUE),
('transaction.read.assigned', 'transaction', 'read', 'Consulter les transactions des clients assignés', TRUE),
('transaction.read.all', 'transaction', 'read', 'Consulter toutes les transactions', TRUE),
('transaction.create', 'transaction', 'create', 'Créer une transaction', TRUE),
('transaction.approve', 'transaction', 'approve', 'Approuver une transaction', TRUE),
('transaction.cancel', 'transaction', 'cancel', 'Annuler une transaction', TRUE),

-- Permissions bénéficiaires
('beneficiary.read.own', 'beneficiary', 'read', 'Consulter ses propres bénéficiaires', TRUE),
('beneficiary.create.own', 'beneficiary', 'create', 'Créer ses propres bénéficiaires', TRUE),
('beneficiary.update.own', 'beneficiary', 'update', 'Modifier ses propres bénéficiaires', TRUE),
('beneficiary.delete.own', 'beneficiary', 'delete', 'Supprimer ses propres bénéficiaires', TRUE),

-- Permissions virements programmés
('scheduled_transfer.read.own', 'scheduled_transfer', 'read', 'Consulter ses propres virements programmés', TRUE),
('scheduled_transfer.create.own', 'scheduled_transfer', 'create', 'Créer ses propres virements programmés', TRUE),
('scheduled_transfer.update.own', 'scheduled_transfer', 'update', 'Modifier ses propres virements programmés', TRUE),
('scheduled_transfer.delete.own', 'scheduled_transfer', 'delete', 'Supprimer ses propres virements programmés', TRUE),

-- Permissions gestionnaire
('client.manage', 'client', 'manage', 'Gérer les clients assignés', TRUE),
('funds.add', 'funds', 'add', 'Ajouter des fonds sur un compte', TRUE),

-- Permissions admin
('system.manage', 'system', 'manage', 'Gérer les paramètres système', TRUE),
('audit.read', 'audit', 'read', 'Consulter les logs d''audit', TRUE),
('support.manage', 'support', 'manage', 'Gérer le support client', TRUE),
('role.manage', 'role', 'manage', 'Gérer les rôles et permissions', TRUE);

-- Association des permissions aux rôles
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT r.id, p.id FROM `roles` r, `permissions` p
WHERE (
  -- Client
  (r.name = 'client' AND p.name IN (
    'user.read.own', 'user.update.own',
    'account.read.own',
    'transaction.read.own', 'transaction.create',
    'beneficiary.read.own', 'beneficiary.create.own', 'beneficiary.update.own', 'beneficiary.delete.own',
    'scheduled_transfer.read.own', 'scheduled_transfer.create.own', 'scheduled_transfer.update.own', 'scheduled_transfer.delete.own'
  ))
  OR
  -- Manager
  (r.name = 'manager' AND p.name IN (
    'user.read.own', 'user.update.own', 'user.read.all',
    'account.read.own', 'account.read.assigned', 'account.create', 'account.update', 'account.suspend',
    'transaction.read.own', 'transaction.read.assigned', 'transaction.create', 'transaction.approve',
    'client.manage', 'funds.add',
    'support.manage'
  ))
  OR
  -- Admin
  (r.name = 'admin' AND p.resource IN ('user', 'account', 'transaction', 'beneficiary', 'scheduled_transfer', 'system', 'audit', 'support', 'role'))
  OR
  -- Super Admin (toutes les permissions)
  (r.name = 'super_admin')
);

-- Insertion des types de comptes
INSERT INTO `account_types` (`name`, `code`, `description`, `category`, `interest_rate`, `minimum_balance`, `daily_transfer_limit`, `monthly_transfer_limit`, `requires_kyc_level`) VALUES
('Compte Courant Standard', 'CHECKING_STANDARD', 'Compte de dépôt à vue pour les opérations quotidiennes', 'checking', 0.0000, 0.00, 3000.00, 30000.00, 1),
('Compte Épargne Classique', 'SAVINGS_CLASSIC', 'Compte d''épargne avec rémunération attractive', 'savings', 2.5000, 100.00, 1000.00, 10000.00, 1),
('Compte Premium', 'PREMIUM_ACCOUNT', 'Compte premium avec avantages exclusifs', 'premium', 1.0000, 5000.00, 10000.00, 100000.00, 2),
('Compte Étudiant', 'STUDENT_ACCOUNT', 'Compte spécialement conçu pour les étudiants', 'checking', 0.5000, 0.00, 1500.00, 15000.00, 1),
('Compte Entreprise', 'BUSINESS_ACCOUNT', 'Compte pour les professionnels et entreprises', 'business', 0.2000, 1000.00, 25000.00, 250000.00, 3);

-- Insertion des catégories de transactions
INSERT INTO `transaction_categories` (`name`, `code`, `description`, `color`, `is_system`) VALUES
('Virements', 'TRANSFERS', 'Virements bancaires internes et externes', '#2196F3', TRUE),
('Prélèvements', 'DIRECT_DEBITS', 'Prélèvements automatiques', '#F44336', TRUE),
('Dépôts', 'DEPOSITS', 'Dépôts d''espèces et de chèques', '#4CAF50', TRUE),
('Retraits', 'WITHDRAWALS', 'Retraits d''espèces', '#FF9800', TRUE),
('Frais bancaires', 'BANK_FEES', 'Frais et commissions bancaires', '#9E9E9E', TRUE),
('Paiements par carte', 'CARD_PAYMENTS', 'Paiements effectués par carte bancaire', '#673AB7', TRUE),
('Alimentation', 'FOOD', 'Achats alimentaires et restaurants', '#8BC34A', FALSE),
('Transport', 'TRANSPORT', 'Frais de transport et carburant', '#9C27B0', FALSE),
('Loisirs', 'ENTERTAINMENT', 'Dépenses de loisirs et divertissement', '#E91E63', FALSE),
('Santé', 'HEALTH', 'Frais médicaux et de santé', '#00BCD4', FALSE),
('Logement', 'HOUSING', 'Loyer, charges et frais de logement', '#795548', FALSE),
('Éducation', 'EDUCATION', 'Frais de scolarité et formation', '#FF5722', FALSE);

-- Insertion des types de transactions
INSERT INTO `transaction_types` (`name`, `code`, `description`, `direction`, `requires_approval`, `fee_percentage`, `fixed_fee`) VALUES
('Virement interne', 'INTERNAL_TRANSFER', 'Transfert entre comptes de la même banque', 'both', FALSE, 0.0000, 0.00),
('Virement SEPA', 'SEPA_TRANSFER', 'Virement dans la zone SEPA', 'debit', FALSE, 0.0000, 0.50),
('Virement international', 'INTERNATIONAL_TRANSFER', 'Virement vers l''étranger hors SEPA', 'debit', TRUE, 0.5000, 15.00),
('Dépôt d''espèces', 'CASH_DEPOSIT', 'Dépôt d''argent liquide', 'credit', FALSE, 0.0000, 0.00),
('Retrait DAB', 'ATM_WITHDRAWAL', 'Retrait depuis un distributeur', 'debit', FALSE, 0.0000, 1.00),
('Paiement par carte', 'CARD_PAYMENT', 'Paiement par carte bancaire', 'debit', FALSE, 0.0000, 0.00),
('Prélèvement SEPA', 'SEPA_DIRECT_DEBIT', 'Prélèvement automatique SEPA', 'debit', FALSE, 0.0000, 0.00),
('Virement entrant', 'INCOMING_TRANSFER', 'Réception d''un virement', 'credit', FALSE, 0.0000, 0.00),
('Frais de tenue de compte', 'ACCOUNT_FEE', 'Frais mensuels de gestion', 'debit', FALSE, 0.0000, 5.00),
('Intérêts créditeurs', 'INTEREST_CREDIT', 'Intérêts versés sur l''épargne', 'credit', FALSE, 0.0000, 0.00);

-- Insertion des paramètres système
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `data_type`, `category`, `description`, `is_public`) VALUES
-- Configuration générale
('app.name', 'NeoBank Unified', 'string', 'general', 'Nom de l''application', TRUE),
('app.version', '1.0.0', 'string', 'general', 'Version de l''application', TRUE),
('app.maintenance_mode', 'false', 'boolean', 'general', 'Mode maintenance activé', TRUE),
('app.default_language', 'fr', 'string', 'general', 'Langue par défaut', TRUE),
('app.supported_languages', '["fr", "en", "es", "de"]', 'json', 'general', 'Langues supportées', TRUE),

-- Sécurité
('security.max_login_attempts', '5', 'integer', 'security', 'Nombre maximum de tentatives de connexion', FALSE),
('security.lockout_duration_minutes', '30', 'integer', 'security', 'Durée de verrouillage en minutes', FALSE),
('security.password_min_length', '8', 'integer', 'security', 'Longueur minimale du mot de passe', FALSE),
('security.require_2fa_for_transfers', 'true', 'boolean', 'security', 'Exiger 2FA pour les virements', FALSE),
('security.session_timeout_minutes', '30', 'integer', 'security', 'Timeout de session en minutes', FALSE),

-- Limites de transaction
('limits.daily_transfer_default', '5000.00', 'float', 'limits', 'Limite quotidienne de virement par défaut', FALSE),
('limits.monthly_transfer_default', '50000.00', 'float', 'limits', 'Limite mensuelle de virement par défaut', FALSE),
('limits.max_beneficiaries_per_user', '50', 'integer', 'limits', 'Nombre maximum de bénéficiaires par utilisateur', FALSE),
('limits.max_scheduled_transfers_per_user', '20', 'integer', 'limits', 'Nombre maximum de virements programmés par utilisateur', FALSE),

-- Frais et commissions
('fees.sepa_transfer', '0.50', 'float', 'fees', 'Frais pour virement SEPA', FALSE),
('fees.international_transfer_percentage', '0.5', 'float', 'fees', 'Pourcentage pour virement international', FALSE),
('fees.international_transfer_fixed', '15.00', 'float', 'fees', 'Frais fixes pour virement international', FALSE),
('fees.atm_withdrawal', '1.00', 'float', 'fees', 'Frais de retrait DAB', FALSE),

-- Notifications
('notifications.email_enabled', 'true', 'boolean', 'notifications', 'Notifications par email activées', FALSE),
('notifications.sms_enabled', 'true', 'boolean', 'notifications', 'Notifications par SMS activées', FALSE),
('notifications.push_enabled', 'true', 'boolean', 'notifications', 'Notifications push activées', FALSE),

-- KYC et conformité
('kyc.require_identity_verification', 'true', 'boolean', 'compliance', 'Vérification d''identité obligatoire', FALSE),
('kyc.max_amount_without_kyc', '1000.00', 'float', 'compliance', 'Montant maximum sans KYC', FALSE),
('compliance.aml_screening_enabled', 'true', 'boolean', 'compliance', 'Contrôle anti-blanchiment activé', FALSE);

-- Insertion des catégories FAQ
INSERT INTO `faq_categories` (`name`, `slug`, `description`, `sort_order`) VALUES
('Comptes', 'comptes', 'Questions sur la gestion des comptes bancaires', 1),
('Virements', 'virements', 'Questions sur les virements et transferts', 2),
('Sécurité', 'securite', 'Questions sur la sécurité et l''authentification', 3),
('Frais', 'frais', 'Questions sur les frais et tarifs', 4),
('Support', 'support', 'Questions sur le support client', 5);

-- Insertion d'exemples FAQ
INSERT INTO `faq_entries` (`category_id`, `question`, `answer`, `language`, `is_featured`, `sort_order`) VALUES
((SELECT id FROM faq_categories WHERE slug = 'comptes'),
 'Comment ouvrir un compte ?',
 'Pour ouvrir un compte, rendez-vous sur notre site web, cliquez sur "Ouvrir un compte" et suivez les étapes. Vous devrez fournir une pièce d''identité valide et un justificatif de domicile.',
 'fr', TRUE, 1),
((SELECT id FROM faq_categories WHERE slug = 'virements'),
 'Quels sont les délais pour un virement SEPA ?',
 'Les virements SEPA sont généralement traités en 1 jour ouvré. Les virements effectués avant 16h sont traités le jour même.',
 'fr', TRUE, 1),
((SELECT id FROM faq_categories WHERE slug = 'securite'),
 'Comment activer l''authentification à deux facteurs ?',
 'Rendez-vous dans vos paramètres de sécurité, cliquez sur "Activer 2FA" et suivez les instructions pour configurer votre application d''authentification.',
 'fr', TRUE, 1);

-- =====================================================================================
-- 15. TRIGGERS ET PROCÉDURES (Exemples)
-- =====================================================================================

-- Trigger pour mettre à jour le solde disponible lors d'une transaction
DELIMITER //
CREATE TRIGGER `update_account_balance_after_transaction`
AFTER INSERT ON `transactions`
FOR EACH ROW
BEGIN
  IF NEW.status = 'completed' THEN
    -- Mise à jour du compte source (débit)
    IF NEW.source_account_id IS NOT NULL THEN
      UPDATE accounts
      SET balance = balance - NEW.amount,
          available_balance = available_balance - NEW.amount,
          last_transaction_at = NEW.processed_at,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = NEW.source_account_id;
    END IF;

    -- Mise à jour du compte destination (crédit)
    IF NEW.destination_account_id IS NOT NULL THEN
      UPDATE accounts
      SET balance = balance + NEW.amount,
          available_balance = available_balance + NEW.amount,
          last_transaction_at = NEW.processed_at,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = NEW.destination_account_id;
    END IF;
  END IF;
END//
DELIMITER ;

-- =====================================================================================
-- FIN DU SCHÉMA UNIFIÉ
-- =====================================================================================
