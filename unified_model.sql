-- =================================================================================================
-- UNIFIED NEO-BANK DATABASE MODEL
-- =================================================================================================
-- This script combines the strengths of the 5 provided models and adds further enhancements
-- to create a robust, reliable, efficient, high-performance, and complete database schema.
-- 
-- Key Features:
-- - Role-Based Access Control (RBAC) with permissions.
-- - Strong security features (2FA, password hashing, login attempt tracking).
-- - Detailed and structured tables for all core banking entities.
-- - Use of UUIDs for public-facing identifiers.
-- - Support for internationalization (i18n) for FAQs.
-- - Integrated support ticket system.
-- - Comprehensive audit logging.
-- - System settings table for flexible configuration.
-- - Clear, consistent, and English naming conventions.
-- =================================================================================================

-- -------------------------------------------------------------------------------------------------
-- PREPARATION
-- -------------------------------------------------------------------------------------------------
SET FOREIGN_KEY_CHECKS=0; -- Disable FK checks for table creation order

-- -------------------------------------------------------------------------------------------------
-- 1. CORE AUTH & RBAC TABLES
-- -------------------------------------------------------------------------------------------------

-- `roles`: Defines user roles within the application (e.g., client, manager, admin).
CREATE TABLE `roles` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(50) NOT NULL UNIQUE COMMENT 'Role name (e.g., client, manager, admin)',
  `description` TEXT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `permissions`: Defines granular permissions for actions on resources.
CREATE TABLE `permissions` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL UNIQUE COMMENT 'Permission name (e.g., account:create)',
  `resource` VARCHAR(50) NOT NULL COMMENT 'The resource this permission applies to (e.g., account)',
  `action` VARCHAR(50) NOT NULL COMMENT 'The action allowed by this permission (e.g., create, read, update, delete)',
  `description` TEXT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_permissions_resource_action` (`resource`, `action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `role_permissions`: Links roles to their granted permissions (many-to-many).
CREATE TABLE `role_permissions` (
  `role_id` INT UNSIGNED NOT NULL,
  `permission_id` INT UNSIGNED NOT NULL,
  PRIMARY KEY (`role_id`, `permission_id`),
  CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_role_permissions_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- -------------------------------------------------------------------------------------------------
-- 2. USER & SECURITY TABLES
-- -------------------------------------------------------------------------------------------------

-- `users`: The central table for all application users.
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  `role_id` INT UNSIGNED NOT NULL,
  `email` VARCHAR(255) NOT NULL UNIQUE,
  `password_hash` VARCHAR(255) NOT NULL,
  `first_name` VARCHAR(100) NOT NULL,
  `last_name` VARCHAR(100) NOT NULL,
  `phone_number` VARCHAR(30) NULL,
  `date_of_birth` DATE NULL,
  `address_street` VARCHAR(255) NULL,
  `address_city` VARCHAR(100) NULL,
  `address_postal_code` VARCHAR(20) NULL,
  `address_country_code` CHAR(2) NULL COMMENT 'ISO 3166-1 alpha-2 country code',
  `preferred_language` CHAR(2) NOT NULL DEFAULT 'fr' COMMENT 'ISO 639-1 language code',
  `status` ENUM('pending_verification', 'active', 'suspended', 'closed') NOT NULL DEFAULT 'pending_verification',
  `assigned_manager_id` BIGINT UNSIGNED NULL COMMENT 'The manager assigned to this client',
  `referral_code` VARCHAR(50) NULL UNIQUE COMMENT 'A user's (manager) referral code for others to use',
  `referred_by_id` BIGINT UNSIGNED NULL COMMENT 'The user who referred this user',
  `email_verified_at` TIMESTAMP NULL,
  `email_verification_token` VARCHAR(255) NULL,
  `password_reset_token` VARCHAR(255) NULL,
  `password_reset_expires_at` TIMESTAMP NULL,
  `two_factor_secret` VARCHAR(255) NULL,
  `two_factor_enabled` BOOLEAN NOT NULL DEFAULT FALSE,
  `last_login_at` TIMESTAMP NULL,
  `last_login_ip` VARCHAR(45) NULL,
  `login_attempts` INT UNSIGNED NOT NULL DEFAULT 0,
  `locked_until` TIMESTAMP NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_users_role_id` (`role_id`),
  INDEX `idx_users_status` (`status`),
  INDEX `idx_users_assigned_manager_id` (`assigned_manager_id`),
  INDEX `idx_users_referred_by_id` (`referred_by_id`),
  CONSTRAINT `fk_users_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_users_assigned_manager` FOREIGN KEY (`assigned_manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_users_referred_by` FOREIGN KEY (`referred_by_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `refresh_tokens`: Stores JWT refresh tokens for persistent sessions.
CREATE TABLE `refresh_tokens` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT UNSIGNED NOT NULL,
  `token_hash` VARCHAR(255) NOT NULL UNIQUE,
  `expires_at` TIMESTAMP NOT NULL,
  `revoked_at` TIMESTAMP NULL,
  `ip_address` VARCHAR(45) NULL,
  `user_agent` TEXT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_refresh_tokens_user_id` (`user_id`),
  CONSTRAINT `fk_refresh_tokens_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- -------------------------------------------------------------------------------------------------
-- 3. BANKING CORE TABLES
-- -------------------------------------------------------------------------------------------------

-- `account_types`: Defines different types of bank accounts available.
CREATE TABLE `account_types` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL UNIQUE,
  `code` VARCHAR(30) NOT NULL UNIQUE COMMENT 'A machine-readable code (e.g., CHECKING_INDIVIDUAL)',
  `description` TEXT NULL,
  `features` JSON NULL COMMENT 'Stores flexible properties like interest rates, fees, limits, etc.',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `accounts`: Represents individual bank accounts held by users.
CREATE TABLE `accounts` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NOT NULL,
  `account_type_id` INT UNSIGNED NOT NULL,
  `account_number` VARCHAR(50) NOT NULL UNIQUE,
  `iban` VARCHAR(34) NULL UNIQUE,
  `bic_swift` VARCHAR(11) NULL,
  `friendly_name` VARCHAR(100) NULL COMMENT 'A user-defined name for the account',
  `balance` DECIMAL(19, 4) NOT NULL DEFAULT 0.0000,
  `available_balance` DECIMAL(19, 4) NOT NULL DEFAULT 0.0000 COMMENT 'Balance minus holds/reservations',
  `currency_code` CHAR(3) NOT NULL DEFAULT 'EUR' COMMENT 'ISO 4217 currency code',
  `status` ENUM('pending_approval', 'active', 'dormant', 'suspended', 'closed') NOT NULL DEFAULT 'pending_approval',
  `overdraft_limit` DECIMAL(19, 4) NOT NULL DEFAULT 0.0000,
  `opened_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `closed_at` TIMESTAMP NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_accounts_user_id` (`user_id`),
  INDEX `idx_accounts_status` (`status`),
  CONSTRAINT `fk_accounts_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_accounts_account_type` FOREIGN KEY (`account_type_id`) REFERENCES `account_types` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `transaction_types`: Defines the nature of a transaction (e.g., transfer, card payment).
CREATE TABLE `transaction_types` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL UNIQUE,
  `code` VARCHAR(50) NOT NULL UNIQUE COMMENT 'A machine-readable code (e.g., CARD_PAYMENT)',
  `direction` ENUM('IN', 'OUT') NOT NULL COMMENT 'Direction of money flow from the source account's perspective',
  `description` TEXT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `transaction_categories`: User-defined categories for personal finance management.
CREATE TABLE `transaction_categories` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT UNSIGNED NULL COMMENT 'Null for system categories, otherwise user-specific',
  `name` VARCHAR(100) NOT NULL,
  `color` VARCHAR(7) NULL COMMENT 'Hex color code for UI',
  `icon` VARCHAR(50) NULL COMMENT 'Icon name for UI',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_category_name` (`user_id`, `name`),
  CONSTRAINT `fk_transaction_categories_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `transactions`: Records all financial movements.
CREATE TABLE `transactions` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  `source_account_id` BIGINT UNSIGNED NULL,
  `destination_account_id` BIGINT UNSIGNED NULL,
  `transaction_type_id` INT UNSIGNED NOT NULL,
  `category_id` INT UNSIGNED NULL,
  `amount` DECIMAL(19, 4) NOT NULL,
  `currency_code` CHAR(3) NOT NULL,
  `description` TEXT NULL,
  `reference` VARCHAR(255) NULL,
  `status` ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed') NOT NULL DEFAULT 'pending',
  `fees` DECIMAL(10, 4) NOT NULL DEFAULT 0.0000,
  `exchange_rate` DECIMAL(12, 6) NULL,
  `initiated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `completed_at` TIMESTAMP NULL,
  `initiated_by_user_id` BIGINT UNSIGNED NULL,
  `external_party_name` VARCHAR(200) NULL,
  `external_party_account` VARCHAR(50) NULL,
  `external_party_bank` VARCHAR(200) NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_transactions_source_account` (`source_account_id`),
  INDEX `idx_transactions_destination_account` (`destination_account_id`),
  INDEX `idx_transactions_type_status` (`transaction_type_id`, `status`),
  CONSTRAINT `fk_transactions_source_account` FOREIGN KEY (`source_account_id`) REFERENCES `accounts` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_transactions_destination_account` FOREIGN KEY (`destination_account_id`) REFERENCES `accounts` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_transactions_type` FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_types` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_transactions_category` FOREIGN KEY (`category_id`) REFERENCES `transaction_categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_transactions_initiated_by` FOREIGN KEY (`initiated_by_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `beneficiaries`: Stores recipient details for easy transfers.
CREATE TABLE `beneficiaries` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NOT NULL,
  `nickname` VARCHAR(100) NOT NULL,
  `full_name` VARCHAR(200) NOT NULL,
  `account_number` VARCHAR(50) NOT NULL,
  `iban` VARCHAR(34) NULL,
  `bic_swift` VARCHAR(11) NULL,
  `bank_name` VARCHAR(200) NULL,
  `bank_address` TEXT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_beneficiary` (`user_id`, `account_number`, `iban`),
  CONSTRAINT `fk_beneficiaries_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `scheduled_transfers`: For recurring or future-dated payments.
CREATE TABLE `scheduled_transfers` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NOT NULL,
  `source_account_id` BIGINT UNSIGNED NOT NULL,
  `destination_account_id` BIGINT UNSIGNED NULL,
  `beneficiary_id` BIGINT UNSIGNED NULL,
  `amount` DECIMAL(19, 4) NOT NULL,
  `currency_code` CHAR(3) NOT NULL,
  `description` TEXT NULL,
  `frequency` ENUM('once', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly') NOT NULL,
  `start_date` DATE NOT NULL,
  `end_date` DATE NULL,
  `next_execution_date` DATE NOT NULL,
  `last_execution_date` DATE NULL,
  `status` ENUM('active', 'paused', 'completed', 'cancelled') NOT NULL DEFAULT 'active',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_scheduled_transfers_status_next_execution` (`status`, `next_execution_date`),
  CONSTRAINT `fk_scheduled_transfers_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_scheduled_transfers_source_account` FOREIGN KEY (`source_account_id`) REFERENCES `accounts` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_scheduled_transfers_destination_account` FOREIGN KEY (`destination_account_id`) REFERENCES `accounts` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_scheduled_transfers_beneficiary` FOREIGN KEY (`beneficiary_id`) REFERENCES `beneficiaries` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `chk_transfer_destination` CHECK (
      (`destination_account_id` IS NOT NULL AND `beneficiary_id` IS NULL) OR
      (`destination_account_id` IS NULL AND `beneficiary_id` IS NOT NULL)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- -------------------------------------------------------------------------------------------------
-- 4. COMMUNICATION & SUPPORT TABLES
-- -------------------------------------------------------------------------------------------------

-- `notifications`: System- or user-generated notifications.
CREATE TABLE `notifications` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `message` TEXT NOT NULL,
  `channel` ENUM('in_app', 'email', 'sms', 'push') NOT NULL DEFAULT 'in_app',
  `priority` ENUM('low', 'medium', 'high') NOT NULL DEFAULT 'medium',
  `link` VARCHAR(255) NULL COMMENT 'A URL to navigate to on click',
  `is_read` BOOLEAN NOT NULL DEFAULT FALSE,
  `read_at` TIMESTAMP NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_notifications_user_read` (`user_id`, `is_read`),
  CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `secure_messages`: For secure communication between users (e.g., client and manager).
CREATE TABLE `secure_messages` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  `parent_message_id` BIGINT UNSIGNED NULL COMMENT 'For threading messages',
  `sender_id` BIGINT UNSIGNED NOT NULL,
  `recipient_id` BIGINT UNSIGNED NOT NULL,
  `subject` VARCHAR(255) NULL,
  `body` TEXT NOT NULL,
  `attachments` JSON NULL COMMENT 'Array of attachment URLs or identifiers',
  `is_read` BOOLEAN NOT NULL DEFAULT FALSE,
  `read_at` TIMESTAMP NULL,
  `deleted_by_sender` BOOLEAN NOT NULL DEFAULT FALSE,
  `deleted_by_recipient` BOOLEAN NOT NULL DEFAULT FALSE,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_secure_messages_conversation` (`sender_id`, `recipient_id`),
  INDEX `idx_secure_messages_recipient_read` (`recipient_id`, `is_read`),
  CONSTRAINT `fk_secure_messages_parent` FOREIGN KEY (`parent_message_id`) REFERENCES `secure_messages` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_secure_messages_sender` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_secure_messages_recipient` FOREIGN KEY (`recipient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `support_tickets`: A system for tracking customer support issues.
CREATE TABLE `support_tickets` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NULL COMMENT 'The user who created the ticket. Null for guests.',
  `guest_name` VARCHAR(150) NULL,
  `guest_email` VARCHAR(255) NULL,
  `assigned_to_id` BIGINT UNSIGNED NULL COMMENT 'The agent assigned to this ticket',
  `subject` VARCHAR(255) NOT NULL,
  `status` ENUM('open', 'pending_customer_reply', 'pending_agent_reply', 'resolved', 'closed') NOT NULL DEFAULT 'open',
  `priority` ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `resolved_at` TIMESTAMP NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_support_tickets_status` (`status`),
  INDEX `idx_support_tickets_user` (`user_id`),
  INDEX `idx_support_tickets_assigned_to` (`assigned_to_id`),
  CONSTRAINT `fk_support_tickets_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_support_tickets_assigned_to` FOREIGN KEY (`assigned_to_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `support_ticket_messages`: Messages within a support ticket thread.
CREATE TABLE `support_ticket_messages` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `ticket_id` BIGINT UNSIGNED NOT NULL,
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT 'The user who wrote the message (can be an agent or the client)',
  `message` TEXT NOT NULL,
  `attachments` JSON NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_support_ticket_messages_ticket` (`ticket_id`),
  CONSTRAINT `fk_support_ticket_messages_ticket` FOREIGN KEY (`ticket_id`) REFERENCES `support_tickets` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_support_ticket_messages_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- -------------------------------------------------------------------------------------------------
-- 5. CONTENT & SYSTEM TABLES
-- -------------------------------------------------------------------------------------------------

-- `faq_categories`: Categories for grouping FAQ entries.
CREATE TABLE `faq_categories` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `sort_order` INT NOT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `faq_category_translations`: Language-specific names for FAQ categories.
CREATE TABLE `faq_category_translations` (
  `category_id` INT UNSIGNED NOT NULL,
  `language_code` CHAR(2) NOT NULL COMMENT 'ISO 639-1',
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT NULL,
  PRIMARY KEY (`category_id`, `language_code`),
  CONSTRAINT `fk_faq_category_translations_category` FOREIGN KEY (`category_id`) REFERENCES `faq_categories` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `faq_entries`: The individual FAQ items.
CREATE TABLE `faq_entries` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` INT UNSIGNED NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `sort_order` INT NOT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_faq_entries_category` FOREIGN KEY (`category_id`) REFERENCES `faq_categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `faq_entry_translations`: Language-specific content for FAQ entries.
CREATE TABLE `faq_entry_translations` (
  `entry_id` INT UNSIGNED NOT NULL,
  `language_code` CHAR(2) NOT NULL COMMENT 'ISO 639-1',
  `question` TEXT NOT NULL,
  `answer` TEXT NOT NULL,
  PRIMARY KEY (`entry_id`, `language_code`),
  CONSTRAINT `fk_faq_entry_translations_entry` FOREIGN KEY (`entry_id`) REFERENCES `faq_entries` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `audit_logs`: Records critical actions performed in the system for security and compliance.
CREATE TABLE `audit_logs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  `user_id` BIGINT UNSIGNED NULL COMMENT 'The user who performed the action. Null for system actions.',
  `action` VARCHAR(100) NOT NULL COMMENT 'e.g., user.login, account.create_failed',
  `resource_type` VARCHAR(50) NULL COMMENT 'The type of resource affected (e.g., user, account)',
  `resource_id` VARCHAR(50) NULL COMMENT 'The ID or UUID of the affected resource',
  `description` TEXT NULL,
  `old_values` JSON NULL,
  `new_values` JSON NULL,
  `ip_address` VARCHAR(45) NULL,
  `user_agent` TEXT NULL,
  `severity` ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_audit_logs_user` (`user_id`),
  INDEX `idx_audit_logs_resource` (`resource_type`, `resource_id`),
  INDEX `idx_audit_logs_action` (`action`),
  CONSTRAINT `fk_audit_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- `system_settings`: A key-value store for application-wide settings.
CREATE TABLE `system_settings` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `key` VARCHAR(100) NOT NULL UNIQUE,
  `value` TEXT NULL,
  `description` TEXT NULL,
  `data_type` ENUM('string', 'integer', 'float', 'boolean', 'json') NOT NULL DEFAULT 'string',
  `is_public` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'If true, can be exposed to the client-side',
  `updated_by_id` BIGINT UNSIGNED NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_system_settings_updated_by` FOREIGN KEY (`updated_by_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- -------------------------------------------------------------------------------------------------
-- FINALIZATION
-- -------------------------------------------------------------------------------------------------
SET FOREIGN_KEY_CHECKS=1; -- Re-enable FK checks

-- =================================================================================================
-- SEED DATA
-- =================================================================================================

-- Roles
INSERT INTO `roles` (`name`, `description`) VALUES
('client', 'Standard client user with access to their own accounts.'),
('manager', 'Account manager responsible for a portfolio of clients.'),
('admin', 'System administrator with broad operational permissions.'),
('support_agent', 'Customer support agent for handling tickets.');

-- Account Types
INSERT INTO `account_types` (`name`, `code`, `description`, `features`, `is_active`) VALUES
('Personal Current Account', 'CURRENT_PERSONAL', 'Standard day-to-day transaction account for individuals.', '{"monthly_fee": 0.00, "overdraft_fee": 25.00, "card_issuance_fee": 0.00}', 1),
('Student Account', 'CURRENT_STUDENT', 'Current account with benefits for students.', '{"monthly_fee": 0.00, "overdraft_fee": 0.00, "card_issuance_fee": 0.00}', 1),
('Classic Savings', 'SAVINGS_CLASSIC', 'A simple savings account.', '{"interest_rate": 1.50, "minimum_deposit": 50.00}', 1);

-- Transaction Types
INSERT INTO `transaction_types` (`name`, `code`, `direction`, `description`) VALUES
('Internal Transfer', 'INTERNAL_TRANSFER', 'OUT', 'Transfer between two accounts within the bank.'),
('External Transfer (SEPA)', 'EXTERNAL_SEPA_TRANSFER', 'OUT', 'Transfer to an external account in the SEPA zone.'),
('Card Payment', 'CARD_PAYMENT', 'OUT', 'Payment made with a debit/credit card.'),
('ATM Withdrawal', 'ATM_WITHDRAWAL', 'OUT', 'Cash withdrawal from an ATM.'),
('Direct Debit', 'DIRECT_DEBIT', 'OUT', 'An automatic payment to a creditor.'),
('Incoming Transfer', 'INCOMING_TRANSFER', 'IN', 'Receiving a transfer from an internal or external account.'),
('Cash Deposit', 'CASH_DEPOSIT', 'IN', 'Depositing cash into an account.'),
('Bank Fee', 'BANK_FEE', 'OUT', 'Fees charged by the bank for services.');

-- System-level Transaction Categories
INSERT INTO `transaction_categories` (`user_id`, `name`, `color`, `icon`) VALUES
(NULL, 'Transfers', '#2196F3', 'transfer'),
(NULL, 'Bills & Utilities', '#F44336', 'receipt'),
(NULL, 'Groceries', '#8BC34A', 'shopping_cart'),
(NULL, 'Transport', '#9C27B0', 'commute'),
(NULL, 'Entertainment', '#E91E63', 'theaters'),
(NULL, 'Fees', '#FF9800', 'account_balance');

-- System Settings
INSERT INTO `system_settings` (`key`, `value`, `description`, `data_type`, `is_public`) VALUES
('app.name', 'Unity NeoBank', 'The public name of the application.', 'string', 1),
('app.version', '1.0.0', 'Current version of the application.', 'string', 1),
('app.maintenance_mode', 'false', 'Set to true to enable maintenance mode.', 'boolean', 1),
('security.max_login_attempts', '5', 'Max failed login attempts before account lockout.', 'integer', 0),
('security.lockout_duration_minutes', '15', 'Duration of account lockout in minutes.', 'integer', 0),
('transfer.daily_limit_client', '10000.00', 'Maximum daily transfer amount for a standard client.', 'float', 0);

-- You would have a more complex script to populate permissions and role_permissions
-- This is a simplified example:
INSERT INTO `permissions` (`name`, `resource`, `action`, `description`) VALUES
('accounts:read:own', 'accounts', 'read', 'Read own accounts'),
('transfers:create:own', 'transfers', 'create', 'Create a transfer from own account'),
('clients:manage:all', 'clients', 'manage', 'Manage all client data (for admins)');

-- Assign permissions to client role
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT (SELECT id FROM `roles` WHERE `name` = 'client'), `id` FROM `permissions` WHERE `name` IN ('accounts:read:own', 'transfers:create:own');

-- Assign all permissions to admin role
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT (SELECT id FROM `roles` WHERE `name` = 'admin'), `id` FROM `permissions` WHERE `name` = 'clients:manage:all';

-- =================================================================================================
-- END OF SCRIPT
-- =================================================================================================
