-- Désactiver temporairement les vérifications de clés étrangères pour la création
SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------------
-- Table: roles
-- ----------------------------------
CREATE TABLE `roles` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(50) UNIQUE NOT NULL COMMENT 'Ex: client, manager, admin',
  `description` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: permissions
-- ----------------------------------
CREATE TABLE `permissions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) UNIQUE NOT NULL COMMENT 'Ex: create_transfer, view_client_profile',
  `description` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: role_permissions
-- ----------------------------------
CREATE TABLE `role_permissions` (
  `role_id` INT NOT NULL,
  `permission_id` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`role_id`, `permission_id`),
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: users
-- ----------------------------------
CREATE TABLE `users` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `role_id` INT NOT NULL,
  `first_name` VARCHAR(100) NOT NULL,
  `last_name` VARCHAR(100) NOT NULL,
  `email` VARCHAR(255) UNIQUE NOT NULL,
  `password_hash` VARCHAR(255) NOT NULL,
  `phone_number` VARCHAR(30) NULL,
  `address_street` VARCHAR(255) NULL,
  `address_city` VARCHAR(100) NULL,
  `address_postal_code` VARCHAR(20) NULL,
  `address_country_code` CHAR(2) NULL COMMENT 'ISO 3166-1 alpha-2',
  `status` ENUM('pending_verification', 'active', 'suspended', 'closed') NOT NULL DEFAULT 'pending_verification',
  `email_verification_token` VARCHAR(100) NULL UNIQUE,
  `email_verified_at` TIMESTAMP NULL,
  `password_reset_token` VARCHAR(100) NULL UNIQUE,
  `password_reset_expires_at` TIMESTAMP NULL,
  `two_factor_secret` VARCHAR(255) NULL,
  `two_factor_enabled` BOOLEAN NOT NULL DEFAULT FALSE,
  `preferred_language` CHAR(2) NOT NULL DEFAULT 'fr' COMMENT 'ISO 639-1',
  `referral_code` VARCHAR(50) NULL UNIQUE COMMENT 'Pour les gestionnaires',
  `assigned_manager_id` BIGINT NULL COMMENT 'FK vers users.id (un manager)',
  `referred_by_manager_id` BIGINT NULL COMMENT 'FK vers users.id (un manager)',
  `last_login_at` TIMESTAMP NULL,
  `last_login_ip` VARCHAR(45) NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_users_email` (`email`),
  INDEX `idx_users_status` (`status`),
  INDEX `idx_users_role_id` (`role_id`),
  INDEX `idx_users_assigned_manager_id` (`assigned_manager_id`),
  INDEX `idx_users_referral_code` (`referral_code`),
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  FOREIGN KEY (`assigned_manager_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY (`referred_by_manager_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: account_types
-- ----------------------------------
CREATE TABLE `account_types` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) UNIQUE NOT NULL,
  `code` VARCHAR(20) UNIQUE NOT NULL COMMENT 'Ex: CHECKING, SAVINGS',
  `description` TEXT NULL,
  `features` JSON NULL COMMENT 'Taux, plafonds, etc.',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: accounts
-- ----------------------------------
CREATE TABLE `accounts` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NOT NULL,
  `account_type_id` INT NOT NULL,
  `account_number` VARCHAR(34) UNIQUE NOT NULL,
  `iban` VARCHAR(34) UNIQUE NULL,
  `bic_swift` VARCHAR(11) NULL,
  `friendly_name` VARCHAR(100) NULL,
  `balance` DECIMAL(15, 2) NOT NULL DEFAULT 0.00,
  `currency_code` CHAR(3) NOT NULL DEFAULT 'EUR' COMMENT 'ISO 4217',
  `status` ENUM('pending_approval', 'active', 'dormant', 'suspended', 'closed') NOT NULL DEFAULT 'pending_approval',
  `opened_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `closed_at` TIMESTAMP NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_accounts_user_id` (`user_id`),
  INDEX `idx_accounts_account_number` (`account_number`),
  INDEX `idx_accounts_status` (`status`),
  INDEX `idx_accounts_account_type_id` (`account_type_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`account_type_id`) REFERENCES `account_types`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: transaction_types
-- ----------------------------------
CREATE TABLE `transaction_types` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(100) UNIQUE NOT NULL,
  `code` VARCHAR(30) UNIQUE NOT NULL COMMENT 'Ex: INTERNAL_TRANSFER, EXTERNAL_TRANSFER',
  `description` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: transactions
-- ----------------------------------
CREATE TABLE `transactions` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `transaction_type_id` INT NOT NULL,
  `source_account_id` BIGINT NULL,
  `destination_account_id` BIGINT NULL,
  `amount` DECIMAL(15, 2) NOT NULL,
  `currency_code` CHAR(3) NOT NULL,
  `description` VARCHAR(255) NULL,
  `reference_number` VARCHAR(50) UNIQUE NOT NULL,
  `status` ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'reversed') NOT NULL DEFAULT 'pending',
  `executed_at` TIMESTAMP NULL,
  `external_beneficiary_name` VARCHAR(150) NULL,
  `external_beneficiary_account` VARCHAR(34) NULL,
  `external_beneficiary_bank_details` TEXT NULL,
  `created_by_user_id` BIGINT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_transactions_source_account_id` (`source_account_id`),
  INDEX `idx_transactions_destination_account_id` (`destination_account_id`),
  INDEX `idx_transactions_status` (`status`),
  INDEX `idx_transactions_transaction_type_id` (`transaction_type_id`),
  INDEX `idx_transactions_executed_at` (`executed_at`),
  FOREIGN KEY (`transaction_type_id`) REFERENCES `transaction_types`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  FOREIGN KEY (`source_account_id`) REFERENCES `accounts`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY (`destination_account_id`) REFERENCES `accounts`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY (`created_by_user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: beneficiaries
-- ----------------------------------
CREATE TABLE `beneficiaries` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NOT NULL,
  `name` VARCHAR(150) NOT NULL,
  `account_number` VARCHAR(34) NOT NULL,
  `bank_name` VARCHAR(100) NULL,
  `bic_swift` VARCHAR(11) NULL,
  `bank_address` TEXT NULL,
  `is_internal` BOOLEAN NOT NULL DEFAULT FALSE,
  `internal_account_id` BIGINT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE INDEX `uidx_beneficiaries_user_account` (`user_id`, `account_number`),
  INDEX `idx_beneficiaries_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`internal_account_id`) REFERENCES `accounts`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: scheduled_transfers
-- ----------------------------------
CREATE TABLE `scheduled_transfers` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NOT NULL,
  `source_account_id` BIGINT NOT NULL,
  `beneficiary_id` BIGINT NULL,
  `destination_internal_account_id` BIGINT NULL,
  `amount` DECIMAL(15, 2) NOT NULL,
  `currency_code` CHAR(3) NOT NULL,
  `description` VARCHAR(255) NULL,
  `frequency` ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'once') NOT NULL,
  `start_date` DATE NOT NULL,
  `end_date` DATE NULL,
  `next_execution_date` DATE NOT NULL,
  `status` ENUM('active', 'paused', 'completed', 'cancelled') NOT NULL DEFAULT 'active',
  `last_execution_status` ENUM('success', 'failed', 'pending') NULL,
  `last_executed_at` TIMESTAMP NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_scheduled_transfers_user_id` (`user_id`),
  INDEX `idx_scheduled_transfers_source_account_id` (`source_account_id`),
  INDEX `idx_scheduled_transfers_status` (`status`),
  INDEX `idx_scheduled_transfers_next_execution_date` (`next_execution_date`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`source_account_id`) REFERENCES `accounts`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`beneficiary_id`) REFERENCES `beneficiaries`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY (`destination_internal_account_id`) REFERENCES `accounts`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `chk_scheduled_transfers_destination` CHECK (
    (`beneficiary_id` IS NOT NULL AND `destination_internal_account_id` IS NULL) OR
    (`beneficiary_id` IS NULL AND `destination_internal_account_id` IS NOT NULL)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: refresh_tokens
-- ----------------------------------
CREATE TABLE `refresh_tokens` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NOT NULL,
  `token_hash` VARCHAR(255) UNIQUE NOT NULL,
  `expires_at` TIMESTAMP NOT NULL,
  `user_agent` TEXT NULL,
  `ip_address` VARCHAR(45) NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `revoked_at` TIMESTAMP NULL,
  INDEX `idx_refresh_tokens_user_id` (`user_id`),
  INDEX `idx_refresh_tokens_token_hash` (`token_hash`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: notifications
-- ----------------------------------
CREATE TABLE `notifications` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NOT NULL,
  `type` VARCHAR(50) NOT NULL COMMENT 'Ex: transaction_completed, security_alert',
  `title` VARCHAR(255) NOT NULL,
  `message` TEXT NOT NULL,
  `link` VARCHAR(255) NULL,
  `is_read` BOOLEAN NOT NULL DEFAULT FALSE,
  `read_at` TIMESTAMP NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_notifications_user_id` (`user_id`),
  INDEX `idx_notifications_user_read_status` (`user_id`, `is_read`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: secure_messages
-- ----------------------------------
CREATE TABLE `secure_messages` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `sender_id` BIGINT NOT NULL,
  `recipient_id` BIGINT NOT NULL,
  `subject` VARCHAR(255) NULL,
  `body` TEXT NOT NULL,
  `is_read_by_recipient` BOOLEAN NOT NULL DEFAULT FALSE,
  `sent_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `read_at` TIMESTAMP NULL,
  `parent_message_id` BIGINT NULL,
  `deleted_by_sender` BOOLEAN NOT NULL DEFAULT FALSE,
  `deleted_by_recipient` BOOLEAN NOT NULL DEFAULT FALSE,
  INDEX `idx_secure_messages_sender_id` (`sender_id`),
  INDEX `idx_secure_messages_recipient_id` (`recipient_id`),
  INDEX `idx_secure_messages_recipient_read_status` (`recipient_id`, `is_read_by_recipient`),
  FOREIGN KEY (`sender_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`recipient_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`parent_message_id`) REFERENCES `secure_messages`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: faq_categories
-- ----------------------------------
CREATE TABLE `faq_categories` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: faq_category_translations
-- ----------------------------------
CREATE TABLE `faq_category_translations` (
  `faq_category_id` INT NOT NULL,
  `language_code` CHAR(2) NOT NULL COMMENT 'ISO 639-1',
  `name` VARCHAR(100) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`faq_category_id`, `language_code`),
  FOREIGN KEY (`faq_category_id`) REFERENCES `faq_categories`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: faq_entries
-- ----------------------------------
CREATE TABLE `faq_entries` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `faq_category_id` INT NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `sort_order` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`faq_category_id`) REFERENCES `faq_categories`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: faq_entry_translations
-- ----------------------------------
CREATE TABLE `faq_entry_translations` (
  `faq_entry_id` INT NOT NULL,
  `language_code` CHAR(2) NOT NULL COMMENT 'ISO 639-1',
  `question` TEXT NOT NULL,
  `answer` TEXT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`faq_entry_id`, `language_code`),
  FOREIGN KEY (`faq_entry_id`) REFERENCES `faq_entries`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: support_tickets
-- ----------------------------------
CREATE TABLE `support_tickets` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NULL,
  `guest_name` VARCHAR(150) NULL,
  `guest_email` VARCHAR(255) NULL,
  `subject` VARCHAR(255) NOT NULL,
  `message` TEXT NOT NULL,
  `status` ENUM('open', 'pending_customer_reply', 'pending_agent_reply', 'resolved', 'closed') NOT NULL DEFAULT 'open',
  `assigned_to_user_id` BIGINT NULL,
  `priority` ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `resolved_at` TIMESTAMP NULL,
  INDEX `idx_support_tickets_user_id` (`user_id`),
  INDEX `idx_support_tickets_status` (`status`),
  INDEX `idx_support_tickets_assigned_to_user_id` (`assigned_to_user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  FOREIGN KEY (`assigned_to_user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: audit_logs
-- ----------------------------------
CREATE TABLE `audit_logs` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NULL,
  `action` VARCHAR(255) NOT NULL,
  `target_type` VARCHAR(100) NULL,
  `target_id` BIGINT NULL,
  `details` JSON NULL,
  `ip_address` VARCHAR(45) NULL,
  `user_agent` TEXT NULL,
  `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_audit_logs_user_id` (`user_id`),
  INDEX `idx_audit_logs_target` (`target_type`, `target_id`),
  INDEX `idx_audit_logs_action` (`action`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------------
-- Table: app_settings
-- ----------------------------------
CREATE TABLE `app_settings` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `setting_key` VARCHAR(100) UNIQUE NOT NULL,
  `setting_value` TEXT NOT NULL,
  `description` TEXT NULL,
  `is_editable_by_admin` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Réactiver les vérifications de clés étrangères
SET FOREIGN_KEY_CHECKS=1;

-- Insérer des données initiales (exemples)
INSERT INTO `roles` (`name`, `description`) VALUES
('client', 'Utilisateur client de la néo-banque'),
('manager', 'Gestionnaire de comptes clients'),
('admin', 'Administrateur de la plateforme');

INSERT INTO `permissions` (`name`, `description`) VALUES
('view_own_profile', 'Voir son propre profil'),
('edit_own_profile', 'Modifier son propre profil'),
('view_own_accounts', 'Voir ses propres comptes'),
('create_internal_transfer', 'Créer un virement interne'),
('create_external_transfer', 'Créer un virement externe'),
('manage_beneficiaries', 'Gérer ses bénéficiaires'),
('view_assigned_clients', 'Voir les clients assignés (manager)'),
('view_client_accounts', 'Voir les comptes d''un client assigné (manager)'),
('suspend_client_account', 'Suspendre le compte d''un client (manager, admin)'),
('edit_client_profile', 'Modifier le profil d''un client (manager, admin)'),
('manage_users', 'Gérer tous les utilisateurs (admin)'),
('manage_roles_permissions', 'Gérer les rôles et permissions (admin)'),
('view_audit_logs', 'Voir les logs d''audit (admin)'),
('manage_app_settings', 'Gérer les paramètres de l''application (admin)');

-- Exemple d'assignation de permissions (simplifié, à adapter finement)
-- Client
INSERT INTO `role_permissions` (`role_id`, `permission_id`) VALUES
((SELECT id FROM roles WHERE name = 'client'), (SELECT id FROM permissions WHERE name = 'view_own_profile')),
((SELECT id FROM roles WHERE name = 'client'), (SELECT id FROM permissions WHERE name = 'edit_own_profile')),
((SELECT id FROM roles WHERE name = 'client'), (SELECT id FROM permissions WHERE name = 'view_own_accounts')),
((SELECT id FROM roles WHERE name = 'client'), (SELECT id FROM permissions WHERE name = 'create_internal_transfer')),
((SELECT id FROM roles WHERE name = 'client'), (SELECT id FROM permissions WHERE name = 'create_external_transfer')),
((SELECT id FROM roles WHERE name = 'client'), (SELECT id FROM permissions WHERE name = 'manage_beneficiaries'));

-- Manager
INSERT INTO `role_permissions` (`role_id`, `permission_id`) VALUES
((SELECT id FROM roles WHERE name = 'manager'), (SELECT id FROM permissions WHERE name = 'view_own_profile')),
((SELECT id FROM roles WHERE name = 'manager'), (SELECT id FROM permissions WHERE name = 'edit_own_profile')),
((SELECT id FROM roles WHERE name = 'manager'), (SELECT id FROM permissions WHERE name = 'view_assigned_clients')),
((SELECT id FROM roles WHERE name = 'manager'), (SELECT id FROM permissions WHERE name = 'view_client_accounts')),
((SELECT id FROM roles WHERE name = 'manager'), (SELECT id FROM permissions WHERE name = 'edit_client_profile')); -- avec consentement

-- Admin
INSERT INTO `role_permissions` (`role_id`, `permission_id`)
SELECT (SELECT id FROM roles WHERE name = 'admin'), id FROM permissions; -- L'admin a toutes les permissions

INSERT INTO `account_types` (`name`, `code`, `description`) VALUES
('Compte Courant Particulier', 'CHECKING_INDIVIDUAL', 'Compte de dépôt à vue pour les opérations quotidiennes.'),
('Compte Épargne Classique', 'SAVINGS_CLASSIC', 'Compte pour mettre de l''argent de côté avec une rémunération modérée.'),
('Compte Étudiant', 'STUDENT_ACCOUNT', 'Compte courant avec avantages pour les étudiants.');

INSERT INTO `transaction_types` (`name`, `code`, `description`) VALUES
('Virement Interne', 'INTERNAL_TRANSFER', 'Transfert entre deux comptes internes à la banque.'),
('Virement Externe SEPA', 'EXTERNAL_SEPA_TRANSFER', 'Transfert vers un compte dans la zone SEPA.'),
('Dépôt d''espèces', 'CASH_DEPOSIT', 'Dépôt d''argent liquide sur un compte.'),
('Retrait DAB', 'ATM_WITHDRAWAL', 'Retrait d''argent depuis un distributeur automatique.'),
('Paiement par Carte', 'CARD_PAYMENT', 'Paiement effectué avec une carte bancaire liée au compte.'),
('Prélèvement SEPA', 'SEPA_DIRECT_DEBIT', 'Prélèvement automatique initié par un créancier.'),
('Frais de Gestion', 'MANAGEMENT_FEE', 'Frais prélevés pour la tenue de compte ou services.'),
('Virement entrant', 'INCOMING_TRANSFER', 'Réception d''un virement sur le compte.');

INSERT INTO `app_settings` (`setting_key`, `setting_value`, `description`, `is_editable_by_admin`) VALUES
('default_language', 'fr', 'Langue par défaut de l''application', TRUE),
('session_timeout_minutes', '30', 'Durée d''inactivité avant déconnexion automatique (en minutes)', TRUE),
('max_failed_login_attempts', '5', 'Nombre max de tentatives de connexion échouées avant verrouillage temporaire', TRUE),
('jwt_access_token_lifetime_seconds', '900', 'Durée de vie du token JWT d''accès (15 minutes)', FALSE),
('jwt_refresh_token_lifetime_days', '30', 'Durée de vie du token JWT de rafraîchissement (30 jours)', FALSE);