-- Base de données complète pour la néo-banque
CREATE DATABASE IF NOT EXISTS neobank_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE neobank_db;

-- Table des rôles
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des permissions
CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_resource_action (resource, action)
);

-- Table d'association rôles-permissions
CREATE TABLE role_permissions (
    role_id INT,
    permission_id INT,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- Table des utilisateurs
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    address TEXT,
    city VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'France',
    role_id INT NOT NULL,
    manager_id INT NULL, -- Gestionnaire affecté (pour les clients)
    referral_code VARCHAR(50) UNIQUE, -- Code de parrainage (pour les gestionnaires)
    referred_by INT NULL, -- Référent (gestionnaire qui a parrainé)
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255),
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_email (email),
    INDEX idx_uuid (uuid),
    INDEX idx_manager (manager_id),
    INDEX idx_referral (referral_code),
    INDEX idx_role (role_id)
);

-- Table des types de comptes
CREATE TABLE account_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    interest_rate DECIMAL(5,4) DEFAULT 0.0000,
    minimum_balance DECIMAL(15,2) DEFAULT 0.00,
    monthly_fee DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des comptes bancaires
CREATE TABLE accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
    user_id INT NOT NULL,
    account_type_id INT NOT NULL,
    account_number VARCHAR(50) NOT NULL UNIQUE,
    iban VARCHAR(34) NOT NULL UNIQUE,
    bic VARCHAR(11) DEFAULT 'NEOBANK01',
    balance DECIMAL(15,2) DEFAULT 0.00,
    available_balance DECIMAL(15,2) DEFAULT 0.00, -- Solde disponible (après réservations)
    currency VARCHAR(3) DEFAULT 'EUR',
    status ENUM('active', 'suspended', 'closed') DEFAULT 'active',
    overdraft_limit DECIMAL(15,2) DEFAULT 0.00,
    created_by INT, -- Gestionnaire ou utilisateur qui a créé le compte
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    closed_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (account_type_id) REFERENCES account_types(id),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user (user_id),
    INDEX idx_account_number (account_number),
    INDEX idx_iban (iban),
    INDEX idx_status (status),
    INDEX idx_uuid (uuid)
);

-- Table des catégories de transactions
CREATE TABLE transaction_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Code couleur hexadécimal
    icon VARCHAR(50),
    is_system BOOLEAN DEFAULT FALSE, -- Catégories système (non modifiables)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des transactions
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
    account_id INT NOT NULL,
    category_id INT,
    type ENUM('credit', 'debit') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'EUR',
    description TEXT,
    reference VARCHAR(100),
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    
    -- Informations du compte destinataire (pour les virements)
    recipient_account_id INT NULL, -- Si virement interne
    recipient_iban VARCHAR(34) NULL, -- Si virement externe
    recipient_name VARCHAR(200) NULL,
    recipient_bank VARCHAR(200) NULL,
    
    -- Métadonnées
    fees DECIMAL(10,2) DEFAULT 0.00,
    exchange_rate DECIMAL(10,6) DEFAULT 1.000000,
    processing_date TIMESTAMP NULL,
    value_date DATE,
    
    -- Traçabilité
    initiated_by INT, -- Utilisateur qui a initié la transaction
    processed_by INT NULL, -- Gestionnaire qui a traité (si applicable)
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_account_id) REFERENCES accounts(id) ON DELETE SET NULL,
    FOREIGN KEY (category_id) REFERENCES transaction_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (initiated_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_account (account_id),
    INDEX idx_uuid (uuid),
    INDEX idx_type_status (type, status),
    INDEX idx_processing_date (processing_date),
    INDEX idx_value_date (value_date),
    INDEX idx_amount (amount),
    INDEX idx_created_at (created_at)
);

-- Table des virements programmés
CREATE TABLE scheduled_transfers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
    user_id INT NOT NULL,
    from_account_id INT NOT NULL,
    to_account_id INT NULL, -- Si virement interne
    recipient_iban VARCHAR(34) NULL, -- Si virement externe
    recipient_name VARCHAR(200) NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'EUR',
    description TEXT,
    
    -- Programmation
    frequency ENUM('once', 'daily', 'weekly', 'monthly', 'yearly') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NULL,
    next_execution DATE NOT NULL,
    last_execution DATE NULL,
    execution_count INT DEFAULT 0,
    max_executions INT NULL,
    
    status ENUM('active', 'paused', 'completed', 'cancelled') DEFAULT 'active',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (from_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (to_account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    
    INDEX idx_user (user_id),
    INDEX idx_next_execution (next_execution),
    INDEX idx_status (status),
    INDEX idx_uuid (uuid)
);

-- Table des bénéficiaires
CREATE TABLE beneficiaries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
    user_id INT NOT NULL,
    name VARCHAR(200) NOT NULL,
    iban VARCHAR(34) NOT NULL,
    bic VARCHAR(11),
    bank_name VARCHAR(200),
    address TEXT,
    is_favorite BOOLEAN DEFAULT FALSE,
    nickname VARCHAR(100), -- Surnom donné par l'utilisateur
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_iban (user_id, iban),
    INDEX idx_user (user_id),
    INDEX idx_iban (iban),
    INDEX idx_uuid (uuid)
);

-- Table des notifications
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
    user_id INT NOT NULL,
    type ENUM('info', 'warning', 'success', 'error') DEFAULT 'info',
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    -- Métadonnées
    data JSON, -- Données supplémentaires (ID de transaction, etc.)
    channel ENUM('web', 'email', 'sms', 'push') DEFAULT 'web',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    
    -- État
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user (user_id),
    INDEX idx_unread (user_id, is_read),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    INDEX idx_uuid (uuid)
);

-- Table des messages (support/communication)
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    parent_message_id INT NULL, -- Pour les fils de discussion
    
    -- État
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    is_archived BOOLEAN DEFAULT FALSE,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    
    -- Métadonnées
    attachments JSON, -- Liste des pièces jointes
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_message_id) REFERENCES messages(id) ON DELETE CASCADE,
    
    INDEX idx_sender (sender_id),
    INDEX idx_recipient (recipient_id),
    INDEX idx_conversation (sender_id, recipient_id),
    INDEX idx_unread (recipient_id, is_read),
    INDEX idx_uuid (uuid)
);

-- Table des journaux d'audit
CREATE TABLE audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uuid CHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
    user_id INT,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL, -- 'user', 'account', 'transaction', etc.
    resource_id VARCHAR(50), -- ID de la ressource affectée
    
    -- Détails
    description TEXT,
    old_values JSON, -- Anciennes valeurs (pour les modifications)
    new_values JSON, -- Nouvelles valeurs
    metadata JSON, -- Métadonnées supplémentaires
    
    -- Contexte
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created_at (created_at),
    INDEX idx_severity (severity),
    INDEX idx_uuid (uuid)
);

-- Table des sessions (optionnelle, pour le tracking)
CREATE TABLE user_sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user (user_id),
    INDEX idx_expires (expires_at),
    INDEX idx_last_activity (last_activity)
);

-- Table des tokens de refresh JWT
CREATE TABLE refresh_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    revoked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user (user_id),
    INDEX idx_token (token_hash),
    INDEX idx_expires (expires_at)
);

-- Table des paramètres système
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    data_type ENUM('string', 'integer', 'float', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Visible côté client
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
);

-- Insertion des données de base
INSERT INTO roles (name, description) VALUES
('client', 'Utilisateur client standard'),
('manager', 'Gestionnaire de compte'),
('admin', 'Administrateur système'),
('super_admin', 'Super administrateur');

INSERT INTO account_types (name, description, interest_rate, minimum_balance) VALUES
('Compte Courant', 'Compte de dépôt standard', 0.0000, 0.00),
('Compte Épargne', 'Compte d''épargne avec intérêts', 1.5000, 100.00),
('Compte Premium', 'Compte premium avec avantages', 0.5000, 5000.00);

INSERT INTO transaction_categories (name, description, color, is_system) VALUES
('Virement', 'Virements bancaires', '#2196F3', TRUE),
('Prélèvement', 'Prélèvements automatiques', '#F44336', TRUE),
('Dépôt', 'Dépôts d''espèces ou chèques', '#4CAF50', TRUE),
('Frais', 'Frais bancaires', '#FF9800', TRUE),
('Alimentation', 'Achats alimentaires', '#8BC34A', FALSE),
('Transport', 'Frais de transport', '#9C27B0', FALSE),
('Loisirs', 'Dépenses de loisirs', '#E91E63', FALSE);

-- Insertion des permissions de base
INSERT INTO permissions (name, resource, action, description) VALUES
-- Permissions utilisateur
('user.read', 'user', 'read', 'Consulter les informations utilisateur'),
('user.update', 'user', 'update', 'Modifier les informations utilisateur'),
('user.delete', 'user', 'delete', 'Supprimer un utilisateur'),

-- Permissions compte
('account.read', 'account', 'read', 'Consulter les comptes'),
('account.create', 'account', 'create', 'Créer un compte'),
('account.update', 'account', 'update', 'Modifier un compte'),
('account.suspend', 'account', 'suspend', 'Suspendre un compte'),

-- Permissions transaction
('transaction.read', 'transaction', 'read', 'Consulter les transactions'),
('transaction.create', 'transaction', 'create', 'Créer une transaction'),
('transaction.process', 'transaction', 'process', 'Traiter une transaction'),

-- Permissions gestionnaire
('client.manage', 'client', 'manage', 'Gérer les clients affectés'),
('funds.add', 'funds', 'add', 'Ajouter des fonds'),

-- Permissions admin
('system.manage', 'system', 'manage', 'Gérer le système'),
('audit.read', 'audit', 'read', 'Consulter les logs d''audit');

-- Association des permissions aux rôles
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p
WHERE (r.name = 'client' AND p.name IN ('user.read', 'user.update', 'account.read', 'transaction.read', 'transaction.create'))
   OR (r.name = 'manager' AND p.name IN ('user.read', 'user.update', 'account.read', 'account.create', 'account.update', 'account.suspend', 'transaction.read', 'transaction.create', 'transaction.process', 'client.manage', 'funds.add'))
   OR (r.name = 'admin' AND p.resource IN ('user', 'account', 'transaction', 'system', 'audit'))
   OR (r.name = 'super_admin');

-- Paramètres système par défaut
INSERT INTO system_settings (setting_key, setting_value, data_type, description, is_public) VALUES
('app.name', 'NeoBank', 'string', 'Nom de l''application', TRUE),
('app.version', '1.0.0', 'string', 'Version de l''application', TRUE),
('app.maintenance', 'false', 'boolean', 'Mode maintenance', TRUE),
('transfer.daily_limit', '5000.00', 'float', 'Limite quotidienne de virement', FALSE),
('transfer.monthly_limit', '50000.00', 'float', 'Limite mensuelle de virement', FALSE),
('security.max_login_attempts', '5', 'integer', 'Nombre maximum de tentatives de connexion', FALSE),
('security.lockout_duration', '30', 'integer', 'Durée de verrouillage en minutes', FALSE);