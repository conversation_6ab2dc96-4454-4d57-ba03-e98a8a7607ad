-- =====================================================================================
-- SCRIPT DE MIGRATION ET VALIDATION POUR SCHÉMA NÉOBANQUE UNIFIÉ
-- =====================================================================================

-- Configuration pour la migration
SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;
SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;
SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';

-- =====================================================================================
-- 1. PROCÉDURES DE VALIDATION
-- =====================================================================================

DELIMITER //

-- Procédure pour valider l'intégrité des comptes
CREATE PROCEDURE ValidateAccountIntegrity()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE account_id BIGINT;
    DECLARE calculated_balance DECIMAL(19,4);
    DECLARE stored_balance DECIMAL(19,4);
    DECLARE cur CURSOR FOR SELECT id, balance FROM accounts WHERE status = 'active';
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    CREATE TEMPORARY TABLE IF NOT EXISTS balance_discrepancies (
        account_id BIGINT,
        stored_balance DECIMAL(19,4),
        calculated_balance DECIMAL(19,4),
        difference DECIMAL(19,4)
    );
    
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO account_id, stored_balance;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- Calculer le solde basé sur les transactions
        SELECT COALESCE(
            SUM(CASE 
                WHEN destination_account_id = account_id THEN amount 
                WHEN source_account_id = account_id THEN -amount 
                ELSE 0 
            END), 0
        ) INTO calculated_balance
        FROM transactions 
        WHERE (source_account_id = account_id OR destination_account_id = account_id)
        AND status = 'completed';
        
        -- Vérifier les écarts
        IF ABS(stored_balance - calculated_balance) > 0.01 THEN
            INSERT INTO balance_discrepancies VALUES (
                account_id, stored_balance, calculated_balance, 
                stored_balance - calculated_balance
            );
        END IF;
    END LOOP;
    CLOSE cur;
    
    -- Afficher les résultats
    SELECT * FROM balance_discrepancies;
    DROP TEMPORARY TABLE balance_discrepancies;
END//

-- Procédure pour générer un numéro de compte IBAN
CREATE FUNCTION GenerateIBAN(account_number VARCHAR(50)) 
RETURNS VARCHAR(34)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE iban VARCHAR(34);
    DECLARE check_digits CHAR(2);
    DECLARE bank_code VARCHAR(5) DEFAULT 'NEOBN';
    DECLARE branch_code VARCHAR(5) DEFAULT '00001';
    
    -- Générer les chiffres de contrôle (simplifié)
    SET check_digits = LPAD(MOD(98 - MOD(CONCAT(bank_code, branch_code, account_number, '1427'), 97), 2), 2, '0');
    
    -- Construire l'IBAN
    SET iban = CONCAT('FR', check_digits, bank_code, branch_code, account_number);
    
    RETURN iban;
END//

-- Procédure pour générer un numéro de référence de transaction unique
CREATE FUNCTION GenerateTransactionReference() 
RETURNS VARCHAR(100)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE ref VARCHAR(100);
    DECLARE counter INT;
    
    -- Obtenir un compteur basé sur la date
    SELECT COUNT(*) + 1 INTO counter 
    FROM transactions 
    WHERE DATE(created_at) = CURDATE();
    
    -- Format: YYYYMMDD-NNNNNN
    SET ref = CONCAT(DATE_FORMAT(NOW(), '%Y%m%d'), '-', LPAD(counter, 6, '0'));
    
    RETURN ref;
END//

-- Procédure pour calculer le score de risque d'une transaction
CREATE FUNCTION CalculateTransactionRiskScore(
    user_id BIGINT,
    amount DECIMAL(19,4),
    destination_country VARCHAR(100),
    time_of_day TIME
) 
RETURNS DECIMAL(3,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE risk_score DECIMAL(3,2) DEFAULT 0.00;
    DECLARE user_avg_amount DECIMAL(19,4);
    DECLARE transaction_count_today INT;
    
    -- Facteur montant (plus c'est élevé, plus c'est risqué)
    SELECT AVG(amount) INTO user_avg_amount 
    FROM transactions t
    JOIN accounts a ON t.source_account_id = a.id
    WHERE a.user_id = user_id 
    AND t.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    IF amount > user_avg_amount * 5 THEN
        SET risk_score = risk_score + 0.30;
    ELSEIF amount > user_avg_amount * 2 THEN
        SET risk_score = risk_score + 0.15;
    END IF;
    
    -- Facteur fréquence
    SELECT COUNT(*) INTO transaction_count_today
    FROM transactions t
    JOIN accounts a ON t.source_account_id = a.id
    WHERE a.user_id = user_id 
    AND DATE(t.created_at) = CURDATE();
    
    IF transaction_count_today > 10 THEN
        SET risk_score = risk_score + 0.25;
    ELSEIF transaction_count_today > 5 THEN
        SET risk_score = risk_score + 0.10;
    END IF;
    
    -- Facteur pays de destination
    IF destination_country NOT IN ('France', 'Germany', 'Spain', 'Italy', 'Belgium', 'Netherlands') THEN
        SET risk_score = risk_score + 0.20;
    END IF;
    
    -- Facteur horaire (transactions nocturnes plus risquées)
    IF time_of_day BETWEEN '22:00:00' AND '06:00:00' THEN
        SET risk_score = risk_score + 0.15;
    END IF;
    
    -- Limiter le score entre 0 et 1
    IF risk_score > 1.00 THEN
        SET risk_score = 1.00;
    END IF;
    
    RETURN risk_score;
END//

DELIMITER ;

-- =====================================================================================
-- 2. TRIGGERS POUR L'INTÉGRITÉ DES DONNÉES
-- =====================================================================================

DELIMITER //

-- Trigger pour générer automatiquement l'IBAN lors de la création d'un compte
CREATE TRIGGER `generate_iban_before_insert` 
BEFORE INSERT ON `accounts` 
FOR EACH ROW
BEGIN
    IF NEW.iban IS NULL OR NEW.iban = '' THEN
        SET NEW.iban = GenerateIBAN(NEW.account_number);
    END IF;
END//

-- Trigger pour générer automatiquement la référence de transaction
CREATE TRIGGER `generate_transaction_reference` 
BEFORE INSERT ON `transactions` 
FOR EACH ROW
BEGIN
    IF NEW.reference_number IS NULL OR NEW.reference_number = '' THEN
        SET NEW.reference_number = GenerateTransactionReference();
    END IF;
END//

-- Trigger pour mettre à jour le compteur d'utilisation des bénéficiaires
CREATE TRIGGER `update_beneficiary_usage` 
AFTER INSERT ON `transactions` 
FOR EACH ROW
BEGIN
    IF NEW.external_beneficiary_iban IS NOT NULL THEN
        UPDATE beneficiaries 
        SET usage_count = usage_count + 1,
            last_used_at = NOW()
        WHERE account_number = NEW.external_beneficiary_iban
        AND user_id = (SELECT user_id FROM accounts WHERE id = NEW.source_account_id);
    END IF;
END//

-- Trigger pour créer automatiquement une notification lors d'une transaction
CREATE TRIGGER `create_transaction_notification` 
AFTER UPDATE ON `transactions` 
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status AND NEW.status IN ('completed', 'failed') THEN
        INSERT INTO notifications (
            user_id, type, category, title, message, channels, 
            related_entity_type, related_entity_id
        )
        SELECT 
            a.user_id,
            CASE WHEN NEW.status = 'completed' THEN 'success' ELSE 'error' END,
            'transaction',
            CASE WHEN NEW.status = 'completed' 
                 THEN 'Transaction réalisée' 
                 ELSE 'Échec de transaction' END,
            CASE WHEN NEW.status = 'completed'
                 THEN CONCAT('Votre transaction de ', NEW.amount, ' ', NEW.currency, ' a été réalisée avec succès.')
                 ELSE CONCAT('Votre transaction de ', NEW.amount, ' ', NEW.currency, ' a échoué.') END,
            '["web", "email"]',
            'transaction',
            NEW.id
        FROM accounts a 
        WHERE a.id = NEW.source_account_id;
    END IF;
END//

-- Trigger pour journaliser les modifications de comptes
CREATE TRIGGER `audit_account_changes` 
AFTER UPDATE ON `accounts` 
FOR EACH ROW
BEGIN
    INSERT INTO audit_logs (
        user_id, action, resource_type, resource_id, resource_name,
        description, old_values, new_values, category, severity
    ) VALUES (
        NEW.user_id,
        'account.update',
        'account',
        NEW.id,
        NEW.friendly_name,
        'Modification des informations du compte',
        JSON_OBJECT(
            'status', OLD.status,
            'balance', OLD.balance,
            'friendly_name', OLD.friendly_name
        ),
        JSON_OBJECT(
            'status', NEW.status,
            'balance', NEW.balance,
            'friendly_name', NEW.friendly_name
        ),
        'account',
        CASE 
            WHEN OLD.status != NEW.status THEN 'high'
            WHEN OLD.balance != NEW.balance THEN 'medium'
            ELSE 'low'
        END
    );
END//

DELIMITER ;

-- =====================================================================================
-- 3. VUES POUR FACILITER LES REQUÊTES
-- =====================================================================================

-- Vue pour les informations complètes des utilisateurs
CREATE VIEW `user_complete_info` AS
SELECT 
    u.id,
    u.uuid,
    u.email,
    u.first_name,
    u.last_name,
    u.phone,
    u.status,
    u.kyc_status,
    u.kyc_level,
    r.name as role_name,
    r.display_name as role_display_name,
    m.first_name as manager_first_name,
    m.last_name as manager_last_name,
    u.created_at,
    u.last_login_at
FROM users u
JOIN roles r ON u.role_id = r.id
LEFT JOIN users m ON u.assigned_manager_id = m.id
WHERE u.deleted_at IS NULL;

-- Vue pour le résumé des comptes
CREATE VIEW `account_summary` AS
SELECT 
    a.id,
    a.uuid,
    a.user_id,
    u.first_name,
    u.last_name,
    a.account_number,
    a.iban,
    a.friendly_name,
    at.name as account_type_name,
    a.balance,
    a.available_balance,
    a.currency,
    a.status,
    a.opened_at,
    (SELECT COUNT(*) FROM transactions t 
     WHERE t.source_account_id = a.id OR t.destination_account_id = a.id) as transaction_count,
    (SELECT MAX(t.created_at) FROM transactions t 
     WHERE t.source_account_id = a.id OR t.destination_account_id = a.id) as last_transaction_date
FROM accounts a
JOIN users u ON a.user_id = u.id
JOIN account_types at ON a.account_type_id = at.id
WHERE a.status != 'closed';

-- Vue pour les transactions avec détails
CREATE VIEW `transaction_details` AS
SELECT 
    t.id,
    t.uuid,
    t.reference_number,
    t.amount,
    t.currency,
    t.description,
    t.status,
    tt.name as transaction_type_name,
    tc.name as category_name,
    sa.account_number as source_account_number,
    sa.user_id as source_user_id,
    CONCAT(su.first_name, ' ', su.last_name) as source_user_name,
    da.account_number as destination_account_number,
    da.user_id as destination_user_id,
    CONCAT(du.first_name, ' ', du.last_name) as destination_user_name,
    t.external_beneficiary_name,
    t.external_beneficiary_iban,
    t.initiated_at,
    t.processed_at,
    t.created_at
FROM transactions t
JOIN transaction_types tt ON t.transaction_type_id = tt.id
LEFT JOIN transaction_categories tc ON t.category_id = tc.id
LEFT JOIN accounts sa ON t.source_account_id = sa.id
LEFT JOIN users su ON sa.user_id = su.id
LEFT JOIN accounts da ON t.destination_account_id = da.id
LEFT JOIN users du ON da.user_id = du.id;

-- =====================================================================================
-- 4. PROCÉDURES DE MAINTENANCE
-- =====================================================================================

DELIMITER //

-- Procédure pour nettoyer les tokens expirés
CREATE PROCEDURE CleanExpiredTokens()
BEGIN
    -- Supprimer les refresh tokens expirés
    DELETE FROM refresh_tokens 
    WHERE expires_at < NOW() OR is_revoked = TRUE;
    
    -- Supprimer les sessions expirées
    DELETE FROM user_sessions 
    WHERE expires_at < NOW();
    
    -- Supprimer les tokens de réinitialisation expirés
    UPDATE users 
    SET password_reset_token = NULL, 
        password_reset_expires_at = NULL
    WHERE password_reset_expires_at < NOW();
    
    -- Supprimer les notifications expirées
    UPDATE notifications 
    SET archived_at = NOW()
    WHERE expires_at < NOW() AND archived_at IS NULL;
    
    SELECT 'Nettoyage des tokens terminé' as message;
END//

-- Procédure pour archiver les anciennes données
CREATE PROCEDURE ArchiveOldData(IN months_to_keep INT)
BEGIN
    DECLARE cutoff_date DATE;
    SET cutoff_date = DATE_SUB(CURDATE(), INTERVAL months_to_keep MONTH);
    
    -- Archiver les anciens logs d'audit (plus de X mois)
    CREATE TABLE IF NOT EXISTS audit_logs_archive LIKE audit_logs;
    
    INSERT INTO audit_logs_archive 
    SELECT * FROM audit_logs 
    WHERE created_at < cutoff_date;
    
    DELETE FROM audit_logs 
    WHERE created_at < cutoff_date;
    
    -- Archiver les anciennes notifications lues
    UPDATE notifications 
    SET archived_at = NOW()
    WHERE created_at < cutoff_date 
    AND is_read = TRUE 
    AND archived_at IS NULL;
    
    SELECT CONCAT('Archivage terminé pour les données antérieures au ', cutoff_date) as message;
END//

DELIMITER ;

-- =====================================================================================
-- 5. REQUÊTES DE VALIDATION POST-MIGRATION
-- =====================================================================================

-- Vérifier l'intégrité référentielle
SELECT 'Vérification de l''intégrité référentielle' as check_name;

-- Utilisateurs sans rôle
SELECT COUNT(*) as users_without_role 
FROM users u 
LEFT JOIN roles r ON u.role_id = r.id 
WHERE r.id IS NULL;

-- Comptes sans utilisateur
SELECT COUNT(*) as accounts_without_user 
FROM accounts a 
LEFT JOIN users u ON a.user_id = u.id 
WHERE u.id IS NULL;

-- Transactions avec comptes inexistants
SELECT COUNT(*) as transactions_with_invalid_accounts
FROM transactions t
LEFT JOIN accounts sa ON t.source_account_id = sa.id
LEFT JOIN accounts da ON t.destination_account_id = da.id
WHERE (t.source_account_id IS NOT NULL AND sa.id IS NULL)
   OR (t.destination_account_id IS NOT NULL AND da.id IS NULL);

-- Vérifier les contraintes de données
SELECT 'Vérification des contraintes de données' as check_name;

-- Utilisateurs avec email invalide
SELECT COUNT(*) as users_with_invalid_email 
FROM users 
WHERE email NOT REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';

-- Comptes avec IBAN invalide
SELECT COUNT(*) as accounts_with_invalid_iban 
FROM accounts 
WHERE iban IS NOT NULL 
AND iban NOT REGEXP '^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$';

-- Transactions avec montant négatif
SELECT COUNT(*) as transactions_with_negative_amount 
FROM transactions 
WHERE amount < 0;

-- Restaurer les paramètres originaux
SET SQL_MODE=@OLD_SQL_MODE;
SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;
SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;
