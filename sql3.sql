CREATE DATABASE neo_bank;
USE neo_bank;

-- Table Utilisateurs
CREATE TABLE Utilisateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role ENUM('client', 'gestionnaire', 'admin') NOT NULL,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    mot_de_passe VARCHAR(255) NOT NULL,
    telephone VARCHAR(20),
    adresse TEXT,
    date_inscription DATETIME DEFAULT CURRENT_TIMESTAMP,
    gestionnaire_id INT,
    lien_parrainage VARCHAR(50) UNIQUE,
    FOREIGN KEY (gestionnaire_id) REFERENCES Utilisateurs(id)
);

-- Table Comptes
CREATE TABLE Comptes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    utilisateur_id INT NOT NULL,
    type ENUM('courant', 'epargne') NOT NULL,
    solde DECIMAL(15,2) DEFAULT 0.00,
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    iban <PERSON>RCHAR(34) NOT NULL,
    bic VARCHAR(11) NOT NULL,
    statut ENUM('actif', 'suspendu') DEFAULT 'actif',
    FOREIGN KEY (utilisateur_id) REFERENCES Utilisateurs(id)
);

-- Table Transactions
CREATE TABLE Transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    compte_source_id INT NOT NULL,
    compte_destinataire_id INT,
    montant DECIMAL(15,2) NOT NULL,
    type ENUM('virement', 'paiement') NOT NULL,
    date DATETIME DEFAULT CURRENT_TIMESTAMP,
    description TEXT,
    statut ENUM('en_attente', 'complete', 'échouée') DEFAULT 'en_attente',
    FOREIGN KEY (compte_source_id) REFERENCES Comptes(id),
    FOREIGN KEY (compte_destinataire_id) REFERENCES Comptes(id)
);

-- Table Bénéficiaires
CREATE TABLE Beneficiaires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    utilisateur_id INT NOT NULL,
    nom VARCHAR(100) NOT NULL,
    iban VARCHAR(34) NOT NULL,
    bic VARCHAR(11) NOT NULL,
    FOREIGN KEY (utilisateur_id) REFERENCES Utilisateurs(id)
);

-- Table Notifications
CREATE TABLE Notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    utilisateur_id INT NOT NULL,
    message TEXT NOT NULL,
    date DATETIME DEFAULT CURRENT_TIMESTAMP,
    lue BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (utilisateur_id) REFERENCES Utilisateurs(id)
);

-- Table Messages
CREATE TABLE Messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    expediteur_id INT NOT NULL,
    destinataire_id INT NOT NULL,
    sujet VARCHAR(255) NOT NULL,
    contenu TEXT NOT NULL,
    date DATETIME DEFAULT CURRENT_TIMESTAMP,
    lu BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (expediteur_id) REFERENCES Utilisateurs(id),
    FOREIGN KEY (destinataire_id) REFERENCES Utilisateurs(id)
);

-- Table Logs
CREATE TABLE Logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    utilisateur_id INT NOT NULL,
    action VARCHAR(255) NOT NULL,
    date DATETIME DEFAULT CURRENT_TIMESTAMP,
    details TEXT,
    FOREIGN KEY (utilisateur_id) REFERENCES Utilisateurs(id)
);

-- Index pour optimisation
CREATE INDEX idx_email ON Utilisateurs(email);
CREATE INDEX idx_utilisateur_id ON Comptes(utilisateur_id);
CREATE INDEX idx_compte_source ON Transactions(compte_source_id);
CREATE INDEX idx_compte_destinataire ON Transactions(compte_destinataire_id);
CREATE INDEX idx_beneficiaire_utilisateur ON Beneficiaires(utilisateur_id);
CREATE INDEX idx_notification_utilisateur ON Notifications(utilisateur_id);
CREATE INDEX idx_message_expediteur ON Messages(expediteur_id);
CREATE INDEX idx_message_destinataire ON Messages(destinataire_id);
CREATE INDEX idx_logs_utilisateur ON Logs(utilisateur_id);